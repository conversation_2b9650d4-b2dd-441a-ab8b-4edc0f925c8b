(()=>{var e={};e.id=2580,e.ids=[2580],e.modules={2507:(e,t,s)=>{"use strict";s.d(t,{x:()=>o});var r=s(34386),a=s(44999);async function o(){let e=await (0,a.UL)();return(0,r.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,s,r){try{e.set({name:t,value:s,...r})}catch(e){}},remove(t,s){try{e.set({name:t,value:"",...s})}catch(e){}}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78284:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>g,routeModule:()=>c,serverHooks:()=>m,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>l});var r={};s.r(r),s.d(r,{GET:()=>d});var a=s(96559),o=s(48088),i=s(37719),n=s(32190),u=s(2507);async function d(e){try{let e=await (0,u.x)(),t=new Date;t.setDate(t.getDate()-1);let[s,r,a]=await Promise.allSettled([e.from("custom_api_configs").select("id").limit(1),e.from("api_keys").select("id").eq("status","active").limit(1),e.from("request_logs").select("id").gte("request_timestamp",t.toISOString()).limit(1)]),o=[],i="operational",d="";"rejected"===s.status?(i="down",d=s.reason?.message||"Connection failed"):s.value.error&&(i="down",d=s.value.error.message),o.push({name:"API Gateway",status:i,details:d,lastChecked:new Date().toISOString()});let c="operational",p="";"rejected"===r.status?(c="down",p=r.reason?.message||"Connection failed"):r.value.error?(c="degraded",p="Error checking active keys"):r.value.data&&0!==r.value.data.length||(c="degraded",p="No active API keys found"),o.push({name:"Routing Engine",status:c,details:p,lastChecked:new Date().toISOString()});let l="operational",m="";"rejected"===a.status?(l="down",m=a.reason?.message||"Connection failed"):a.value.error?(l="degraded",m="Error checking recent logs"):a.value.data&&0!==a.value.data.length||(l="degraded",m="No recent activity logged"),o.push({name:"Analytics",status:l,details:m,lastChecked:new Date().toISOString()});let g=o.some(e=>"down"===e.status),h=o.some(e=>"degraded"===e.status),x="operational";g?x="down":h&&(x="degraded");let v=n.NextResponse.json({overall_status:x,checks:o,last_updated:new Date().toISOString()});return v.headers.set("Cache-Control","public, max-age=30, stale-while-revalidate=60"),v.headers.set("X-Content-Type-Options","nosniff"),v}catch(e){return n.NextResponse.json({overall_status:"down",checks:[{name:"System Check",status:"down",details:"Failed to perform system health checks",lastChecked:new Date().toISOString()}],last_updated:new Date().toISOString(),error:e.message},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/system-status/route",pathname:"/api/system-status",filename:"route",bundlePath:"app/api/system-status/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\system-status\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:p,workUnitAsyncStorage:l,serverHooks:m}=c;function g(){return(0,i.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:l})}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580,9398,4999,4386],()=>s(78284));module.exports=r})();