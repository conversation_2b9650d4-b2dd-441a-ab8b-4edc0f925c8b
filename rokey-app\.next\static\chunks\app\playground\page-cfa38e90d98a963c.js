(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3882],{306:(e,t,a)=>{"use strict";a.d(t,{vQ:()=>r.A,QG:()=>s,BZ:()=>i.A});var r=a(30192),n=a(12115);let s=n.forwardRef(function(e,t){let{title:a,titleId:r,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),a?n.createElement("title",{id:r},a):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5 12h14"}))});var i=a(86474)},10747:(e,t,a)=>{"use strict";a.d(t,{EF:()=>r.A,DQ:()=>n.A,nr:()=>i,Y3:()=>o,XL:()=>l.A,$p:()=>c,R2:()=>d.A,P:()=>u.A,Zu:()=>h.A,BZ:()=>m.A,Gg:()=>x.A});var r=a(5279),n=a(64274),s=a(12115);let i=s.forwardRef(function(e,t){let{title:a,titleId:r,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},n),a?s.createElement("title",{id:r},a):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m6.75 7.5 3 2.25-3 2.25m4.5 0h3m-9 8.25h13.5A2.25 2.25 0 0 0 21 18V6a2.25 2.25 0 0 0-2.25-2.25H5.25A2.25 2.25 0 0 0 3 6v12a2.25 2.25 0 0 0 2.25 2.25Z"}))}),o=s.forwardRef(function(e,t){let{title:a,titleId:r,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},n),a?s.createElement("title",{id:r},a):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.362 5.214A8.252 8.252 0 0 1 12 21 8.25 8.25 0 0 1 6.038 7.047 8.287 8.287 0 0 0 9 9.601a8.983 8.983 0 0 1 3.361-6.867 8.21 8.21 0 0 0 3 2.48Z"}),s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 18a3.75 3.75 0 0 0 .495-7.468 5.99 5.99 0 0 0-1.925 3.547 5.975 5.975 0 0 1-2.133-1.001A3.75 3.75 0 0 0 12 18Z"}))});var l=a(48666);let c=s.forwardRef(function(e,t){let{title:a,titleId:r,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},n),a?s.createElement("title",{id:r},a):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))});var d=a(61316),u=a(65946),h=a(8246),m=a(86474),x=a(27305)},11485:(e,t,a)=>{"use strict";a.d(t,{B:()=>n.A,v:()=>r.A});var r=a(30192),n=a(86474)},21208:(e,t,a)=>{"use strict";a.d(t,{E:()=>r.A,D:()=>s});var r=a(5279),n=a(12115);let s=n.forwardRef(function(e,t){let{title:a,titleId:r,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),a?n.createElement("title",{id:r},a):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m19.5 8.25-7.5 7.5-7.5-7.5"}))})},22261:(e,t,a)=>{"use strict";a.d(t,{G:()=>i,c:()=>o});var r=a(95155),n=a(12115);let s=(0,n.createContext)(void 0);function i(e){let{children:t}=e,[a,i]=(0,n.useState)(!0),[o,l]=(0,n.useState)(!1),[c,d]=(0,n.useState)(!1);return(0,r.jsx)(s.Provider,{value:{isCollapsed:a,isHovered:o,isHoverDisabled:c,toggleSidebar:()=>i(!a),collapseSidebar:()=>i(!0),expandSidebar:()=>i(!1),setHovered:e=>{c||l(e)},setHoverDisabled:e=>{d(e),e&&l(!1)}},children:t})}function o(){let e=(0,n.useContext)(s);if(void 0===e)throw Error("useSidebar must be used within a SidebarProvider");return e}},24766:(e,t,a)=>{Promise.resolve().then(a.bind(a,79021))},30192:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var r=a(12115);let n=r.forwardRef(function(e,t){let{title:a,titleId:n,...s}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},s),a?r.createElement("title",{id:n},a):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 0 1-.825-.242m9.345-8.334a2.126 2.126 0 0 0-.476-.095 48.64 48.64 0 0 0-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0 0 11.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155"}))})},60875:(e,t,a)=>{"use strict";a.d(t,{BZ:()=>i.A,C1:()=>n.A,YE:()=>s.A,fl:()=>r.A});var r=a(89416),n=a(6865),s=a(58397),i=a(86474)},76032:(e,t,a)=>{"use strict";a.d(t,{S:()=>r.A,X:()=>s});var r=a(29337),n=a(12115);let s=n.forwardRef(function(e,t){let{title:a,titleId:r,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),a?n.createElement("title",{id:r},a):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 7.5V6.108c0-1.135.845-2.098 1.976-2.192.373-.03.748-.057 1.123-.08M15.75 18H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08M15.75 18.75v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5A3.375 3.375 0 0 0 6.375 7.5H5.25m11.9-3.664A2.251 2.251 0 0 0 15 2.25h-1.5a2.251 2.251 0 0 0-2.15 1.586m5.8 0c.065.21.1.433.1.664v.75h-6V4.5c0-.231.035-.454.1-.664M6.75 7.5H4.875c-.621 0-1.125.504-1.125 1.125v12c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V16.5a9 9 0 0 0-9-9Z"}))})},79021:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>b});var r=a(95155),n=a(12115);let s=n.forwardRef(function(e,t){let{title:a,titleId:r,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),a?n.createElement("title",{id:r},a):null,n.createElement("path",{fillRule:"evenodd",d:"M19.916 4.626a.75.75 0 0 1 .208 1.04l-9 13.5a.75.75 0 0 1-1.154.114l-6-6a.75.75 0 0 1 1.06-1.06l5.353 5.353 8.493-12.74a.75.75 0 0 1 1.04-.207Z",clipRule:"evenodd"}))}),i=n.forwardRef(function(e,t){let{title:a,titleId:r,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),a?n.createElement("title",{id:r},a):null,n.createElement("path",{d:"M3.478 2.404a.75.75 0 0 0-.926.941l2.432 7.905H13.5a.75.75 0 0 1 0 1.5H4.984l-2.432 7.905a.75.75 0 0 0 .926.94 60.519 60.519 0 0 0 18.445-8.986.75.75 0 0 0 0-1.218A60.517 60.517 0 0 0 3.478 2.404Z"}))}),o=n.forwardRef(function(e,t){let{title:a,titleId:r,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),a?n.createElement("title",{id:r},a):null,n.createElement("path",{fillRule:"evenodd",d:"M18.97 3.659a2.25 2.25 0 0 0-3.182 0l-10.94 10.94a3.75 3.75 0 1 0 5.304 5.303l7.693-7.693a.75.75 0 0 1 1.06 1.06l-7.693 7.693a5.25 5.25 0 1 1-7.424-7.424l10.939-10.94a3.75 3.75 0 1 1 5.303 5.304L9.097 18.835l-.008.008-.007.007-.002.002-.003.002A2.25 2.25 0 0 1 5.91 15.66l7.81-7.81a.75.75 0 0 1 1.061 1.06l-7.81 7.81a.75.75 0 0 0 1.054 1.068L18.97 6.84a2.25 2.25 0 0 0 0-3.182Z",clipRule:"evenodd"}))}),l=n.forwardRef(function(e,t){let{title:a,titleId:r,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),a?n.createElement("title",{id:r},a):null,n.createElement("path",{fillRule:"evenodd",d:"M16.5 4.478v.227a48.816 48.816 0 0 1 3.878.512.75.75 0 1 1-.256 1.478l-.209-.035-1.005 13.07a3 3 0 0 1-2.991 2.77H8.084a3 3 0 0 1-2.991-2.77L4.087 6.66l-.209.035a.75.75 0 0 1-.256-1.478A48.567 48.567 0 0 1 7.5 4.705v-.227c0-1.564 1.213-2.9 2.816-2.951a52.662 52.662 0 0 1 3.369 0c1.603.051 2.815 1.387 2.815 2.951Zm-6.136-1.452a51.196 51.196 0 0 1 3.273 0C14.39 3.05 15 3.684 15 4.478v.113a49.488 49.488 0 0 0-6 0v-.113c0-.794.609-1.428 1.364-1.452Zm-.355 5.945a.75.75 0 1 0-1.5.058l.347 9a.75.75 0 1 0 1.499-.058l-.346-9Zm5.48.058a.75.75 0 1 0-1.498-.058l-.347 9a.75.75 0 0 0 1.5.058l.345-9Z",clipRule:"evenodd"}))}),c=n.forwardRef(function(e,t){let{title:a,titleId:r,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),a?n.createElement("title",{id:r},a):null,n.createElement("path",{fillRule:"evenodd",d:"M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm-1.72 6.97a.75.75 0 1 0-1.06 1.06L10.94 12l-1.72 1.72a.75.75 0 1 0 1.06 1.06L12 13.06l1.72 1.72a.75.75 0 1 0 1.06-1.06L13.06 12l1.72-1.72a.75.75 0 1 0-1.06-1.06L12 10.94l-1.72-1.72Z",clipRule:"evenodd"}))}),d=n.forwardRef(function(e,t){let{title:a,titleId:r,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),a?n.createElement("title",{id:r},a):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"}))});var u=a(79112),h=a(95803),m=a(8413),x=a(43456),g=a(62074),p=a(4654),f=a(22261),y=a(24403),v=a(41e3);a(96121);let w=n.memo(e=>{let{chat:t,currentConversation:a,onLoadChat:n,onDeleteChat:s}=e,i=(null==a?void 0:a.id)===t.id;return(0,r.jsxs)("div",{className:"relative group p-3 hover:bg-gray-50 rounded-xl transition-all duration-200 ".concat(i?"bg-orange-50 border border-orange-200":""),children:[(0,r.jsx)("button",{onClick:()=>n(t),className:"w-full text-left",children:(0,r.jsx)("div",{className:"flex items-start justify-between",children:(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900 truncate mb-1",children:t.title}),t.last_message_preview&&(0,r.jsx)("p",{className:"text-xs text-gray-500 line-clamp-2 mb-2",children:t.last_message_preview}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-400",children:[(0,r.jsxs)("span",{children:[t.message_count," messages"]}),(0,r.jsx)("span",{children:new Date(t.updated_at).toLocaleDateString()})]})]})})}),(0,r.jsx)("button",{onClick:e=>{e.stopPropagation(),s(t.id)},className:"absolute top-2 right-2 opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded transition-all duration-200",title:"Delete conversation",children:(0,r.jsx)(l,{className:"w-4 h-4"})})]})});function b(){let{isCollapsed:e,isHovered:t,setHoverDisabled:a}=(0,f.c)(),l=!e||t?"256px":"64px",[b,j]=(0,n.useState)([]),[N,k]=(0,n.useState)(""),[S,E]=(0,n.useState)(!0);(0,n.useEffect)(()=>{N&&fetch("/api/keys?custom_config_id=".concat(N)).then(e=>e.json()).catch(e=>console.log("Background key prefetch failed:",e))},[N]);let[C,T]=(0,n.useState)(""),[A,L]=(0,n.useState)([]),[O,_]=(0,n.useState)(!1),[M,I]=(0,n.useState)(null),[H,R]=(0,n.useState)(!0),[D,P]=(0,n.useState)(!1),[B,z]=(0,n.useState)([]),[W,F]=(0,n.useState)([]),Y=(0,n.useRef)(null),U=(0,n.useRef)(null),Z=(0,n.useRef)(null),[V,J]=(0,n.useState)(!1),[q,X]=(0,n.useState)(null),[K,Q]=(0,n.useState)(null),[G,$]=(0,n.useState)(""),[ee,et]=(0,n.useState)(!1),[ea,er]=(0,n.useState)(null),[en,es]=(0,n.useState)(!1),[ei,eo]=(0,n.useState)(!1),[el,ec]=(0,n.useState)(!1),[ed,eu]=(0,n.useState)(!1),[eh,em]=(0,n.useState)(!1);(0,n.useEffect)(()=>{a(el&&!ed)},[el,ed,a]),(0,n.useEffect)(()=>{},[en,ea,el,ed]);let ex=(0,v.w6)({enableAutoProgression:!0,onStageChange:(e,t)=>{}}),[eg,ep]=(0,n.useState)(""),ef=(e,t)=>{let a="";if(e.includes("\uD83C\uDFAC **Multi-Role AI Orchestration Started!**"))a="Multi-Role AI Orchestration Started";else if(e.includes("\uD83D\uDCCB **Orchestration Plan:**"))a="Planning specialist assignments";else if(e.includes("\uD83E\uDD16 **Moderator:**"))a="Moderator coordinating specialists";else if(e.includes("Specialist:")&&e.includes("Working...")){let t=e.match(/(\w+)\s+Specialist:/);a=t?"".concat(t[1]," Specialist working"):"Specialist working on your request"}else e.includes("\uD83C\uDFAD **SYNTHESIS PHASE INITIATED**")?a="Synthesizing specialist responses":e.includes("Analyzing and processing")&&(a="Analyzing and processing with specialized expertise");a&&a!==eg&&(ep(a),t.updateOrchestrationStatus(a))},ey=async()=>{if(N&&q){_(!0),ep("Continuing synthesis automatically..."),ex.startProcessing();try{let a={id:Date.now().toString()+"-continue",role:"user",content:[{type:"text",text:"continue"}]};L(e=>[...e,a]),await eH(q.id,a);let r={custom_api_config_id:N,messages:[...A.map(e=>({role:e.role,content:1===e.content.length&&"text"===e.content[0].type?e.content[0].text:e.content})),{role:"user",content:"continue"}],stream:H},n=await fetch("/api/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat("Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13")},body:JSON.stringify(r),cache:"no-store"});if(n.ok){let a,r=await n.text();try{a=JSON.parse(r)}catch(e){a=null}if((null==a?void 0:a.error)==="synthesis_complete"){L(e=>e.slice(0,-1)),_(!1),ep(""),ex.markComplete(),T("continue"),setTimeout(()=>{eZ()},100);return}let s=new Response(r,{status:n.status,statusText:n.statusText,headers:n.headers});if(H&&s.body){let a=s.body.getReader(),r=new TextDecoder,n=Date.now().toString()+"-assistant-continue",i={id:n,role:"assistant",content:[{type:"text",text:""}]};L(e=>[...e,i]);let o="",l=!1,c=null,d=s.headers.get("X-Synthesis-Progress"),u=s.headers.get("X-Synthesis-Complete"),h=null!==d;for(h?(ex.markStreaming(),ep("")):(ex.markOrchestrationStarted(),ep("Continuing synthesis..."),c=setTimeout(()=>{l||(ex.markStreaming(),ep(""))},800));;){let{done:s,value:d}=await a.read();if(s)break;for(let a of r.decode(d,{stream:!0}).split("\n"))if(a.startsWith("data: ")){let r=a.substring(6);if("[DONE]"===r.trim())break;try{var e,t;let a=JSON.parse(r);if(a.choices&&(null==(t=a.choices[0])||null==(e=t.delta)?void 0:e.content)){let e=a.choices[0].delta.content;o+=e,!h&&!l&&(e.includes("\uD83C\uDFAC **Multi-Role AI Orchestration Started!**")||e.includes("\uD83D\uDCCB **Orchestration Plan:**")||e.includes("\uD83C\uDFAD **SYNTHESIS PHASE INITIATED**")||e.includes("\uD83E\uDD16 **Moderator:**")||e.includes("Specialist:"))?(l=!0,c&&(clearTimeout(c),c=null),ef(e,ex)):!h&&l&&ef(e,ex);let t=i.content[0];t.text=o,L(e=>e.map(e=>e.id===n?{...e,content:[t]}:e))}}catch(e){}}}if(c&&clearTimeout(c),o){let e={...i,content:[{type:"text",text:o}]};h&&"true"!==u&&o.includes("[SYNTHESIS CONTINUES AUTOMATICALLY...]")?(await eH(q.id,e),setTimeout(()=>{ey()},1e3)):await eH(q.id,e)}}}else throw Error("Auto-continuation failed: ".concat(n.status))}catch(t){let e={id:Date.now().toString()+"-error-continue",role:"error",content:[{type:"text",text:"Auto-continuation failed: ".concat(t instanceof Error?t.message:"Unknown error")}]};L(t=>[...t,e])}finally{_(!1),ep(""),ex.markComplete()}}},{chatHistory:ev,isLoading:ew,isStale:eb,error:ej,refetch:eN,prefetch:ek,invalidateCache:eS}=(0,y.mx)({configId:N,enablePrefetch:!0,cacheTimeout:3e5,staleTimeout:3e4}),{prefetchChatHistory:eE}=(0,y.l2)();(0,n.useEffect)(()=>{(async()=>{try{S&&await new Promise(e=>setTimeout(e,50));let e=await fetch("/api/custom-configs");if(!e.ok){let t=await e.json();throw Error(t.error||"Failed to fetch configurations")}let t=await e.json();j(t),t.length>0&&k(t[0].id),E(!1)}catch(e){I("Failed to load configurations: ".concat(e.message)),j([]),E(!1)}})()},[S]);let eC=e=>new Promise((t,a)=>{let r=new FileReader;r.readAsDataURL(e),r.onload=()=>t(r.result),r.onerror=e=>a(e)}),eT=async e=>{let t=Array.from(e.target.files||[]);if(0===t.length)return;let a=B.length,r=t.slice(0,10-a);r.length<t.length&&I("You can only upload up to 10 images. ".concat(t.length-r.length," images were not added."));try{let e=[];for(let t of r){let a=await eC(t);e.push(a)}z(e=>[...e,...r]),F(t=>[...t,...e])}catch(e){I("Failed to process one or more images. Please try again.")}Y.current&&(Y.current.value="")},eA=e=>{void 0!==e?(z(t=>t.filter((t,a)=>a!==e)),F(t=>t.filter((t,a)=>a!==e))):(z([]),F([])),Y.current&&(Y.current.value="")},eL=function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];Z.current&&Z.current.scrollTo({top:Z.current.scrollHeight,behavior:e?"smooth":"auto"})};(0,n.useEffect)(()=>{A.length>0&&requestAnimationFrame(()=>{eL()})},[A.length]),(0,n.useEffect)(()=>{O&&A.length>0&&requestAnimationFrame(()=>{eL()})},[A,O]),(0,n.useEffect)(()=>{if(O&&A.length>0){let e=A[A.length-1];e&&"assistant"===e.role&&requestAnimationFrame(()=>{eL()})}},[A,O]),(0,n.useEffect)(()=>{let e=setTimeout(()=>{A.length>0&&requestAnimationFrame(()=>{if(Z.current){let e=Z.current;e.scrollHeight-e.scrollTop-e.clientHeight<100&&eL()}})},200);return()=>clearTimeout(e)},[e,t,V,A.length]),(0,n.useEffect)(()=>{if(N&&b.length>0){let e=b.filter(e=>e.id!==N).slice(0,3),t=setTimeout(()=>{e.forEach(e=>{eE(e.id)})},2e3);return()=>clearTimeout(t)}},[N,b,eE]);let eO=async function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];t||et(!0);try{let a=t?A.length:0,r=Date.now(),n=await fetch("/api/chat/messages?conversation_id=".concat(e.id,"&limit=").concat(50,"&offset=").concat(a,"&latest=").concat(!t,"&_cb=").concat(r),{cache:"no-store",headers:{"Cache-Control":"no-cache"}});if(!n.ok)throw Error("Failed to load conversation messages");let s=(await n.json()).map(e=>({id:e.id,role:e.role,content:e.content.map(e=>{var t;return"text"===e.type&&e.text?{type:"text",text:e.text}:"image_url"===e.type&&(null==(t=e.image_url)?void 0:t.url)?{type:"image_url",image_url:{url:e.image_url.url}}:{type:"text",text:""}})}));t?L(e=>[...s,...e]):(L(s),q&&q.id===e.id||X(e)),I(null)}catch(e){I("Failed to load conversation: ".concat(e.message))}finally{t||et(!1)}},e_=async()=>{if(!N||0===A.length)return null;try{let e=null==q?void 0:q.id;if(!e){let t=A[0],a="New Chat";if(t&&t.content.length>0){let e=t.content.find(e=>"text"===e.type);e&&e.text&&(a=e.text.slice(0,50)+(e.text.length>50?"...":""))}let r={custom_api_config_id:N,title:a},n=await fetch("/api/chat/conversations",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)});if(!n.ok)throw Error("Failed to create conversation");let s=await n.json();e=s.id,X(s)}for(let t of A){if(t.id.includes("-")&&t.id.length>20)continue;let a={conversation_id:e,role:t.role,content:t.content};await fetch("/api/chat/messages",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)})}return q||eN(!0),e}catch(e){return I("Failed to save conversation: ".concat(e.message)),null}},eM=async e=>{try{if(!(await fetch("/api/chat/conversations?id=".concat(e),{method:"DELETE"})).ok)throw Error("Failed to delete conversation");(null==q?void 0:q.id)===e&&(X(null),L([])),eN(!0)}catch(e){I("Failed to delete conversation: ".concat(e.message))}},eI=async e=>{if(!N)return null;try{let t="New Chat";if(e.content.length>0){let a=e.content.find(e=>"text"===e.type);a&&a.text&&(t=a.text.slice(0,50)+(a.text.length>50?"...":""))}let a={custom_api_config_id:N,title:t},r=await fetch("/api/chat/conversations",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!r.ok)throw Error("Failed to create conversation");let n=await r.json();return X(n),n.id}catch(e){return I("Failed to create conversation: ".concat(e.message)),null}},eH=async(e,t)=>{try{let a={conversation_id:e,role:t.role,content:t.content},r=await fetch("/api/chat/messages",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!r.ok)throw Error("Failed to save message");return await r.json()}catch(e){}},eR=e=>{T(e),setTimeout(()=>{let e=document.querySelector('textarea[placeholder*="Type a message"]');e&&(e.focus(),e.setSelectionRange(e.value.length,e.value.length))},100)},eD=async()=>{A.length>0&&await e_(),L([]),X(null),T(""),I(null),eA(),ex.reset()},eP=async e=>{if(e===N)return;A.length>0&&await eD(),k(e);let t=b.find(t=>t.id===e);t&&t.name},eB=async e=>{X(e),L([]),T(""),I(null),eA();let t=(async()=>{if(A.length>0&&!q)try{await e_()}catch(e){}})();try{await eO(e)}catch(e){I("Failed to load conversation: ".concat(e.message))}await t},ez=(e,t)=>{Q(e),$(t)},eW=()=>{Q(null),$("")},eF=async()=>{if(!K||!G.trim()||!N)return;let e=A.findIndex(e=>e.id===K);if(-1===e)return;let t=[...A];t[e]={...t[e],content:[{type:"text",text:G.trim()}]};let a=t.slice(0,e+1);if(L(a),Q(null),$(""),q)try{if(A.slice(e+1).length>0){let t=A[e],a=parseInt(t.id)||Date.now(),r=await fetch("/api/chat/messages/delete-after-timestamp",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({conversation_id:q.id,after_timestamp:a})});r.ok&&await r.json()}let t=a[e],r=await fetch("/api/chat/messages/update-by-timestamp",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({conversation_id:q.id,timestamp:parseInt(t.id),content:t.content})});r.ok?await r.json():await eH(q.id,t),eN(!0),Object.keys(localStorage).filter(e=>e.startsWith("chat_")||e.startsWith("conversation_")).forEach(e=>localStorage.removeItem(e))}catch(e){I("Failed to update conversation: ".concat(e.message))}await eY(a)},eY=async e=>{if(!N||0===e.length)return;_(!0),I(null),ex.startProcessing();let t=e.filter(e=>"user"===e.role||"assistant"===e.role||"system"===e.role).map(e=>{let t;if("system"===e.role){let a=e.content[0];t=a&&"text"===a.type?a.text:""}else t=1===e.content.length&&"text"===e.content[0].type?e.content[0].text:e.content.map(e=>"image_url"===e.type?{type:"image_url",image_url:{url:e.image_url.url}}:{type:"text",text:e.text});return{role:e.role,content:t}});try{var a,r,n,s,i,o,l;ex.updateStage("connecting"),performance.now();let e=await fetch("/api/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat("Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13")},body:JSON.stringify({custom_api_config_id:N,messages:t,stream:H}),cache:"no-store"});if(performance.now(),!e.ok){let t=await e.json();throw Error(t.error||"API Error: ".concat(e.statusText," (Status: ").concat(e.status,")"))}if(ex.analyzeResponseHeaders(e.headers),setTimeout(()=>{H&&ex.markStreaming()},400),H&&e.body){let t=e.body.getReader(),n=new TextDecoder,s=Date.now().toString()+"-assistant",i={id:s,role:"assistant",content:[{type:"text",text:""}]};L(e=>[...e,i]);let o="",l=!1,c=null;for(c=setTimeout(()=>{l||ex.markStreaming()},400);;){let{done:e,value:d}=await t.read();if(e)break;for(let e of n.decode(d,{stream:!0}).split("\n"))if(e.startsWith("data: ")){let t=e.substring(6);if("[DONE]"===t.trim())break;try{let e=JSON.parse(t);if(e.choices&&(null==(r=e.choices[0])||null==(a=r.delta)?void 0:a.content)){let t=e.choices[0].delta.content;o+=t,!l&&(t.includes("\uD83C\uDFAC **Multi-Role AI Orchestration Started!**")||t.includes("\uD83D\uDCCB **Orchestration Plan:**")||t.includes("\uD83C\uDFAD **SYNTHESIS PHASE INITIATED**")||t.includes("\uD83E\uDD16 **Moderator:**")||t.includes("Specialist:"))&&(l=!0,c&&(clearTimeout(c),c=null),ex.markOrchestrationStarted()),l&&ef(t,ex);let a=i.content[0];a.text=o,L(e=>e.map(e=>e.id===s?{...e,content:[a]}:e))}}catch(e){}}}if(c&&clearTimeout(c),o&&q){let e={...i,content:[{type:"text",text:o}]};o.includes("[SYNTHESIS CONTINUES AUTOMATICALLY...]")||o.includes("*The response will continue automatically in a new message...*")?(await eH(q.id,e),setTimeout(()=>{ey()},2e3)):await eH(q.id,e)}}else{let t=await e.json(),a="Could not parse assistant's response.";(null==(i=t.choices)||null==(s=i[0])||null==(n=s.message)?void 0:n.content)?a=t.choices[0].message.content:(null==(l=t.content)||null==(o=l[0])?void 0:o.text)?a=t.content[0].text:"string"==typeof t.text&&(a=t.text);let r={id:Date.now().toString()+"-assistant",role:"assistant",content:[{type:"text",text:a}]};L(e=>[...e,r]),q&&await eH(q.id,r)}}catch(t){let e={id:Date.now().toString()+"-error",role:"error",content:[{type:"text",text:t.message||"An unexpected error occurred."}]};L(t=>[...t,e]),I(t.message)}finally{_(!1),ex.markComplete()}},eU=async(e,t)=>{if(!N||e<0||e>=A.length||"assistant"!==A[e].role)return;_(!0),I(null),ep(""),ex.startProcessing();let a=A.slice(0,e);if(L(a),q)try{if(A.slice(e).length>0){let t=A[e],a=parseInt(t.id)||Date.now(),r=await fetch("/api/chat/messages/delete-after-timestamp",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({conversation_id:q.id,from_timestamp:a})});r.ok&&await r.json()}eN(!0)}catch(e){}let r={custom_api_config_id:N,messages:a.filter(e=>"user"===e.role||"assistant"===e.role||"system"===e.role).map(e=>{let t;if("system"===e.role){let a=e.content[0];t=a&&"text"===a.type?a.text:""}else t=1===e.content.length&&"text"===e.content[0].type?e.content[0].text:e.content.map(e=>"image_url"===e.type?{type:"image_url",image_url:{url:e.image_url.url}}:{type:"text",text:e.text});return{role:e.role,content:t}}),stream:H,...t&&{specific_api_key_id:t}};try{ex.updateStage("connecting"),performance.now();let e=await fetch("/api/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat("Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13")},body:JSON.stringify(r),cache:"no-store"});if(performance.now(),!e.ok){let t=await e.json();throw Error(t.error||"HTTP ".concat(e.status,": ").concat(e.statusText))}ex.analyzeResponseHeaders(e.headers);let t=e.headers.get("X-RoKey-Orchestration-ID"),a=e.headers.get("X-RoKey-Orchestration-Active");if(t&&"true"===a&&(er(t),es(!0),eo(!1)),setTimeout(()=>{H&&ex.markStreaming()},400),H&&e.body){let t=e.body.getReader(),a=new TextDecoder,r="",o={id:Date.now().toString()+"-assistant-retry",role:"assistant",content:[{type:"text",text:""}]};L(e=>[...e,o]);try{for(;;){let{done:e,value:l}=await t.read();if(e)break;for(let e of a.decode(l,{stream:!0}).split("\n"))if(e.startsWith("data: ")){let t=e.slice(6);if("[DONE]"===t)continue;try{var n,s,i;let e=JSON.parse(t);if(null==(i=e.choices)||null==(s=i[0])||null==(n=s.delta)?void 0:n.content){let t=e.choices[0].delta.content;r+=t,t.includes("\uD83C\uDFAC **Multi-Role AI Orchestration Started!**")||t.includes("\uD83D\uDCCB **Orchestration Plan:**")||t.includes("\uD83C\uDFAD **SYNTHESIS PHASE INITIATED**")||t.includes("\uD83E\uDD16 **Moderator:**")||t.includes("Specialist:")?(ex.markOrchestrationStarted(),ef(t,ex)):eg&&ef(t,ex),L(e=>e.map(e=>e.id===o.id?{...e,content:[{type:"text",text:r}]}:e))}}catch(e){}}}}finally{t.releaseLock()}if(r&&q){let e={...o,content:[{type:"text",text:r}]};await eH(q.id,e)}}else{let t=await e.json(),a="";t.choices&&t.choices.length>0&&t.choices[0].message?a=t.choices[0].message.content:t.content&&Array.isArray(t.content)&&t.content.length>0&&(a=t.content[0].text);let r={id:Date.now().toString()+"-assistant-retry",role:"assistant",content:[{type:"text",text:a}]};L(e=>[...e,r]),q&&await eH(q.id,r)}}catch(t){let e={id:Date.now().toString()+"-error-retry",role:"error",content:[{type:"text",text:t.message||"An unexpected error occurred during retry."}]};L(t=>[...t,e]),I(t.message),q&&await eH(q.id,e)}finally{_(!1),ex.markComplete()}},eZ=async e=>{if(e&&e.preventDefault(),!C.trim()&&0===B.length||!N)return;if("continue"===C.trim().toLowerCase()&&A.length>0){T(""),await ey();return}_(!0),I(null),ep(""),ex.startProcessing(),performance.now();let t=C.trim(),a=[...B],r=[...W];T(""),eA();let n=[],s=[];if(t&&(n.push({type:"text",text:t}),s.push({type:"text",text:t})),a.length>0)try{for(let e=0;e<a.length;e++){let t=a[e],i=r[e],o=await eC(t);n.push({type:"image_url",image_url:{url:i}}),s.push({type:"image_url",image_url:{url:o}})}}catch(e){I("Failed to process one or more images. Please try again."),_(!1),T(t),z(a),F(r);return}let i={id:Date.now().toString(),role:"user",content:n};L(e=>[...e,i]);let o=null==q?void 0:q.id,l=Promise.resolve(o||null);Promise.resolve(),o||q||(l=eI(i)),l.then(async e=>{e&&await eH(e,i)}).catch(e=>{});let c={custom_api_config_id:N,messages:[...A.filter(e=>"user"===e.role||"assistant"===e.role||"system"===e.role).map(e=>{let t;if("system"===e.role){let a=e.content[0];t=a&&"text"===a.type?a.text:""}else t=1===e.content.length&&"text"===e.content[0].type?e.content[0].text:e.content.map(e=>"image_url"===e.type?{type:"image_url",image_url:{url:e.image_url.url}}:{type:"text",text:e.text});return{role:e.role,content:t}}),{role:"user",content:1===s.length&&"text"===s[0].type?s[0].text:s}],stream:H};try{ex.updateStage("connecting"),performance.now();let e=await fetch("/api/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat("Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13")},body:JSON.stringify(c),cache:"no-store"});if(performance.now(),!e.ok){let t=await e.json();throw Error(t.error||"API Error: ".concat(e.statusText," (Status: ").concat(e.status,")"))}ex.analyzeResponseHeaders(e.headers);let t=e.headers.get("X-RoKey-Orchestration-ID"),a=e.headers.get("X-RoKey-Orchestration-Active");if(t&&"true"===a&&(er(t),es(!0),eo(!1)),H&&e.body){let t=e.body.getReader(),a=new TextDecoder,r=Date.now().toString()+"-assistant",n={id:r,role:"assistant",content:[{type:"text",text:""}]};L(e=>[...e,n]);let s="",i=!1,o=null;for(o=setTimeout(()=>{i||ex.markStreaming()},400);;){let{done:e,value:l}=await t.read();if(e)break;for(let e of a.decode(l,{stream:!0}).split("\n"))if(e.startsWith("data: ")){let t=e.substring(6);if("[DONE]"===t.trim())break;try{var d,u;let e=JSON.parse(t);if(e.choices&&(null==(u=e.choices[0])||null==(d=u.delta)?void 0:d.content)){let t=e.choices[0].delta.content;s+=t,!i&&(t.includes("\uD83C\uDFAC **Multi-Role AI Orchestration Started!**")||t.includes("\uD83D\uDCCB **Orchestration Plan:**")||t.includes("\uD83C\uDFAD **SYNTHESIS PHASE INITIATED**")||t.includes("\uD83E\uDD16 **Moderator:**")||t.includes("Specialist:"))&&(i=!0,o&&(clearTimeout(o),o=null),ex.markOrchestrationStarted()),i&&ef(t,ex);let a=n.content[0];a.text=s,L(e=>e.map(e=>e.id===r?{...e,content:[a]}:e))}}catch(e){}}}if(o&&clearTimeout(o),s){let t={...n,content:[{type:"text",text:s}]},a=e.headers.get("X-Synthesis-Progress");e.headers.get("X-Synthesis-Complete"),s.includes("[SYNTHESIS CONTINUES AUTOMATICALLY...]")||s.includes("*The response will continue automatically in a new message...*")?(l.then(async e=>{e&&await eH(e,t)}),setTimeout(()=>{ey()},null!==a?1e3:2e3)):l.then(async e=>{e&&await eH(e,t)})}}}catch(t){let e={id:Date.now().toString()+"-error",role:"error",content:[{type:"text",text:t.message||"An unexpected error occurred."}]};L(t=>[...t,e]),I(t.message),l.then(async t=>{t&&await eH(t,e)}).catch(e=>{})}finally{_(!1),ex.markComplete(),(0,v.n4)(ex.stageHistory),performance.now(),l.then(async e=>{e&&!q&&eN(!0)}).catch(e=>{})}};return(0,r.jsxs)("div",{className:"min-h-screen bg-[#faf8f5] flex",children:[(0,r.jsxs)("div",{className:"flex-1 flex flex-col transition-all duration-300 ease-in-out",style:{marginLeft:l,marginRight:el&&!ed?"50%":V?"0px":"320px"},children:[(0,r.jsx)("div",{className:"fixed top-0 z-40 bg-[#faf8f5]/95 backdrop-blur-sm border-b border-gray-200/30 transition-all duration-300 ease-in-out",style:{left:l,right:el&&!ed?"50%":V?"0px":"320px"},children:(0,r.jsx)("div",{className:"px-6 py-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"flex items-center space-x-2",children:N?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-600",children:"Connected"})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Not Connected"})]})}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("select",{value:N,onChange:e=>eP(e.target.value),disabled:0===b.length,className:"appearance-none px-4 py-2.5 pr-10 bg-white/90 border border-gray-200/50 rounded-xl text-sm font-medium text-gray-700 focus:outline-none focus:ring-2 focus:ring-orange-500/20 focus:border-orange-300 transition-all duration-200 shadow-sm hover:shadow-md min-w-[200px]",children:[(0,r.jsx)("option",{value:"",children:"Select Router"}),b.map(e=>(0,r.jsx)("option",{value:e.id,children:e.name},e.id))]}),(0,r.jsx)("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-gray-600",children:"Streaming"}),(0,r.jsx)("button",{onClick:()=>R(!H),className:"relative inline-flex h-7 w-12 items-center rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500/20 shadow-sm ".concat(H?"bg-orange-500 shadow-orange-200":"bg-gray-300"),children:(0,r.jsx)("span",{className:"inline-block h-5 w-5 transform rounded-full bg-white transition-transform duration-200 shadow-sm ".concat(H?"translate-x-6":"translate-x-1")})})]})]})})}),(0,r.jsx)("div",{className:"flex-1 flex flex-col pt-20 pb-32",children:0!==A.length||q?(0,r.jsxs)("div",{className:"flex-1 relative ".concat(el&&!ed?"overflow-visible":"overflow-hidden"),children:[(0,r.jsx)("div",{className:"h-full flex ".concat(el&&!ed?"justify-start":"justify-center"),children:(0,r.jsx)("div",{ref:Z,className:"w-full h-full overflow-y-auto px-6 transition-all duration-300 ".concat(el&&!ed?"max-w-2xl -ml-32":"max-w-4xl"),onScroll:e=>{let t=e.currentTarget;P(!(t.scrollHeight-t.scrollTop-t.clientHeight<100)&&A.length>0)},children:(0,r.jsxs)("div",{className:"space-y-6 py-8",children:[q&&A.length>=50&&(0,r.jsx)("div",{className:"text-center py-4",children:(0,r.jsx)("button",{onClick:()=>eO(q,!0),disabled:ew,className:"px-4 py-2 text-sm text-orange-600 hover:text-orange-700 hover:bg-orange-50 rounded-lg transition-colors duration-200 disabled:opacity-50",children:ew?"Loading...":"Load Earlier Messages"})}),ee&&0===A.length&&(0,r.jsx)("div",{className:"space-y-4",children:Array.from({length:3}).map((e,t)=>(0,r.jsxs)("div",{className:"flex justify-start",children:[(0,r.jsx)("div",{className:"w-7 h-7 rounded-full bg-gray-200 animate-pulse mr-3 mt-1 flex-shrink-0"}),(0,r.jsx)("div",{className:"max-w-[65%] bg-gray-100 rounded-2xl rounded-bl-lg px-4 py-3 animate-pulse",children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-5/6"})]})})]},t))}),A.map((e,t)=>(0,r.jsxs)("div",{className:"flex ".concat("user"===e.role?"justify-end":"justify-start"," group ").concat(el&&!ed?"-ml-96":""," ").concat("assistant"===e.role&&el&&!ed?"ml-8":""),children:["assistant"===e.role&&(0,r.jsx)("div",{className:"w-7 h-7 rounded-full bg-orange-50 flex items-center justify-center mr-3 mt-1 flex-shrink-0 border border-orange-100",children:(0,r.jsx)("svg",{className:"w-3.5 h-3.5 text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})}),(0,r.jsxs)("div",{className:"".concat("user"===e.role?el&&!ed?"max-w-[60%]":"max-w-[50%]":el&&!ed?"max-w-[85%]":"max-w-[75%]"," relative ").concat("user"===e.role?"bg-orange-600 text-white rounded-2xl rounded-br-lg shadow-sm":"assistant"===e.role?"card text-gray-900 rounded-2xl rounded-bl-lg":"system"===e.role?"bg-amber-50 text-amber-800 rounded-xl border border-amber-200":"bg-red-50 text-red-800 rounded-xl border border-red-200"," px-4 py-3 transition-all duration-300"),children:["user"===e.role&&(0,r.jsxs)("div",{className:"absolute -top-8 right-0 z-10 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:[(0,r.jsx)(h.A,{text:e.content.filter(e=>"text"===e.type).map(e=>e.text).join("\n"),variant:"message",size:"sm",title:"Copy message",className:"text-gray-500 hover:text-gray-700 hover:bg-white/20 rounded-lg cursor-pointer"}),(0,r.jsx)("button",{onClick:()=>ez(e.id,e.content.filter(e=>"text"===e.type).map(e=>e.text).join("\n")),className:"p-1.5 text-gray-500 hover:text-gray-700 hover:bg-white/20 rounded-lg transition-all duration-200 cursor-pointer",title:"Edit message",children:(0,r.jsx)(d,{className:"w-4 h-4 stroke-2"})})]}),"user"!==e.role&&(0,r.jsxs)("div",{className:"absolute -bottom-8 left-0 z-10 flex items-center space-x-2",children:[(0,r.jsx)(h.A,{text:e.content.filter(e=>"text"===e.type).map(e=>e.text).join("\n"),variant:"message",size:"sm",title:"Copy message"}),"assistant"===e.role&&N&&(0,r.jsx)(m.A,{configId:N,onRetry:e=>eU(t,e),disabled:O})]}),(0,r.jsx)("div",{className:"space-y-2 chat-message-content",children:"user"===e.role&&K===e.id?(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("textarea",{value:G,onChange:e=>$(e.target.value),className:"w-full p-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50 resize-none",placeholder:"Edit your message...",rows:3,autoFocus:!0}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("button",{onClick:eF,disabled:!G.trim(),className:"flex items-center space-x-1 px-3 py-1.5 bg-white/20 hover:bg-white/30 disabled:bg-white/10 disabled:opacity-50 text-white text-sm rounded-lg transition-all duration-200",children:[(0,r.jsx)(s,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Save & Continue"})]}),(0,r.jsxs)("button",{onClick:eW,className:"flex items-center space-x-1 px-3 py-1.5 bg-white/10 hover:bg-white/20 text-white text-sm rounded-lg transition-all duration-200",children:[(0,r.jsx)(c,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Cancel"})]})]}),(0,r.jsx)("p",{className:"text-white/70 text-xs",children:"\uD83D\uDCA1 Saving will restart the conversation from this point, removing all messages that came after."})]}):e.content.map((t,a)=>{if("text"===t.type)if("assistant"===e.role)return(0,r.jsx)(u.A,{content:t.text,className:"text-sm"},a);else return(0,r.jsx)("div",{className:"whitespace-pre-wrap break-words leading-relaxed text-sm",children:t.text},a);return"image_url"===t.type?(0,r.jsx)("img",{src:t.image_url.url,alt:"uploaded content",className:"max-w-full max-h-48 rounded-xl shadow-sm"},a):null})})]}),"user"===e.role&&(0,r.jsx)("div",{className:"w-7 h-7 rounded-full bg-orange-600 flex items-center justify-center ml-3 mt-1 flex-shrink-0",children:(0,r.jsx)("svg",{className:"w-3.5 h-3.5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})})]},e.id)),en&&ea&&ed&&(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsx)(p.f,{orchestrationComplete:ei,onMaximize:()=>{em(!0),setTimeout(()=>em(!1),100)},isCanvasOpen:el,isCanvasMinimized:ed})}),O&&(0,r.jsx)(x.A,{currentStage:ex.currentStage,isStreaming:H&&"typing"===ex.currentStage,orchestrationStatus:eg,onStageChange:e=>{}}),en&&ea&&(0,r.jsx)(g.w,{executionId:ea,onCanvasStateChange:(e,t)=>{ec(e),eu(t),e&&!t&&J(!0)},forceMaximize:eh,onComplete:e=>{if(null==ea?void 0:ea.startsWith("test-execution-id"))return void eo(!0);eo(!0);let t={id:Date.now().toString()+"-orchestration-final",role:"assistant",content:[{type:"text",text:e}]};L(e=>[...e,t]),es(!1),er(null),eo(!1),(null==q?void 0:q.id)&&eH(q.id,t).catch(e=>{})},onError:e=>{null!=ea&&ea.startsWith("test-execution-id")||(I("Orchestration error: ".concat(e)),es(!1),er(null))}}),(0,r.jsx)("div",{ref:U})]})})}),D&&(0,r.jsx)("div",{className:"absolute bottom-6 left-1/2 transform -translate-x-1/2 z-10",children:(0,r.jsx)("button",{onClick:()=>eL(!0),className:"w-12 h-12 bg-white rounded-full shadow-lg border border-gray-200/50 flex items-center justify-center hover:shadow-xl transition-all duration-200 hover:scale-105 group","aria-label":"Scroll to bottom",children:(0,r.jsx)("svg",{className:"w-5 h-5 text-gray-600 group-hover:text-orange-600 transition-colors",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 14l-7 7m0 0l-7-7m7 7V3"})})})})]}):(0,r.jsx)("div",{className:"flex-1 flex items-center justify-center px-6 overflow-hidden",children:(0,r.jsx)("div",{className:"w-full mx-auto transition-all duration-300 ".concat(el&&!ed?"max-w-2xl -ml-32":"max-w-4xl"),children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,r.jsxs)("div",{className:"text-center mb-12",children:[(0,r.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Welcome to RoKey"}),(0,r.jsx)("p",{className:"text-lg text-gray-600 max-w-md mx-auto",children:"Get started by selecting a router and choosing a conversation starter below. Not sure where to start?"})]}),(0,r.jsx)("div",{className:"grid grid-cols-2 gap-4 w-full max-w-2xl",children:[{id:"write-copy",title:"Write copy",description:"Create compelling marketing content",icon:"✍️",color:"bg-amber-100 text-amber-700",prompt:"Help me write compelling copy for my product landing page"},{id:"image-generation",title:"Image generation",description:"Create visual content descriptions",icon:"\uD83C\uDFA8",color:"bg-blue-100 text-blue-700",prompt:"Help me create detailed prompts for AI image generation"},{id:"create-avatar",title:"Create avatar",description:"Design character personas",icon:"\uD83D\uDC64",color:"bg-green-100 text-green-700",prompt:"Help me create a detailed character avatar for my story"},{id:"write-code",title:"Write code",description:"Generate and debug code",icon:"\uD83D\uDCBB",color:"bg-purple-100 text-purple-700",prompt:"Help me write clean, efficient code for my project"}].map(e=>(0,r.jsxs)("button",{onClick:()=>eR(e.prompt),disabled:!N,className:"group relative p-6 bg-white rounded-2xl border border-gray-200/50 hover:border-orange-300 hover:shadow-lg transition-all duration-200 text-left disabled:opacity-50 disabled:cursor-not-allowed ".concat(N?"cursor-pointer hover:scale-[1.02]":"cursor-not-allowed"),children:[(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 rounded-xl flex items-center justify-center text-xl ".concat(e.color," group-hover:scale-110 transition-transform duration-200"),children:e.icon}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900 mb-1 group-hover:text-orange-600 transition-colors",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-gray-600 leading-relaxed",children:e.description})]})]}),(0,r.jsx)("div",{className:"absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:(0,r.jsx)("svg",{className:"w-5 h-5 text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4l8 8-8 8M4 12h16"})})})]},e.id))})]})})})}),(0,r.jsx)("div",{className:"fixed bottom-0 z-50 transition-all duration-300 ease-in-out",style:{left:l,right:el&&!ed?"50%":V?"0px":"320px"},children:(0,r.jsx)("div",{className:"px-6 pt-3 pb-2 flex justify-center",children:(0,r.jsxs)("div",{className:"w-full transition-all duration-300 ".concat(el&&!ed?"max-w-2xl":"max-w-4xl"),children:[M&&(0,r.jsx)("div",{className:"mb-4 bg-red-50 border border-red-200 rounded-2xl p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("svg",{className:"w-5 h-5 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,r.jsx)("p",{className:"text-red-800 text-sm font-medium",children:M})]})}),!1,(0,r.jsxs)("form",{onSubmit:eZ,children:[W.length>0&&(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-orange-500 rounded-full"}),(0,r.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:[W.length," image",W.length>1?"s":""," attached"]})]}),(0,r.jsx)("button",{type:"button",onClick:()=>eA(),className:"text-xs text-gray-500 hover:text-red-600 transition-colors duration-200 font-medium",children:"Clear all"})]}),(0,r.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3",children:W.map((e,t)=>(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsxs)("div",{className:"relative overflow-hidden rounded-xl border-2 border-gray-100 bg-white shadow-sm hover:shadow-md transition-all duration-200 aspect-square",children:[(0,r.jsx)("img",{src:e,alt:"Preview ".concat(t+1),className:"w-full h-full object-cover"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-200"}),(0,r.jsx)("button",{type:"button",onClick:()=>eA(t),className:"absolute -top-1 -right-1 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center transition-all duration-200 shadow-lg opacity-0 group-hover:opacity-100 transform scale-90 group-hover:scale-100","aria-label":"Remove image ".concat(t+1),children:(0,r.jsx)(c,{className:"w-3.5 h-3.5"})})]}),(0,r.jsx)("div",{className:"absolute bottom-1 right-1 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded-md font-medium",children:t+1})]},t))})]}),(0,r.jsx)("div",{className:"relative bg-white rounded-2xl border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-200 focus-within:ring-2 focus-within:ring-orange-500/20 focus-within:border-orange-300",children:(0,r.jsxs)("div",{className:"flex items-end p-4 space-x-3",children:[(0,r.jsx)("input",{type:"file",accept:"image/*",multiple:!0,onChange:eT,ref:Y,className:"hidden",id:"imageUpload"}),(0,r.jsxs)("button",{type:"button",onClick:()=>{var e;return null==(e=Y.current)?void 0:e.click()},disabled:B.length>=10,className:"relative p-2 rounded-xl transition-all duration-200 flex-shrink-0 ".concat(B.length>=10?"text-gray-300 cursor-not-allowed":"text-gray-400 hover:text-orange-500 hover:bg-orange-50"),"aria-label":B.length>=10?"Maximum 10 images reached":"Attach images",title:B.length>=10?"Maximum 10 images reached":"Attach images (up to 10)",children:[(0,r.jsx)(o,{className:"w-5 h-5"}),B.length>0&&(0,r.jsx)("div",{className:"absolute -top-1 -right-1 w-4 h-4 bg-orange-500 text-white text-xs rounded-full flex items-center justify-center font-bold",children:B.length})]}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("textarea",{value:C,onChange:e=>T(e.target.value),placeholder:N?"Type a message...":"Select a router first",disabled:!N||O,rows:1,className:"w-full px-0 py-2 bg-transparent border-0 text-gray-900 placeholder-gray-400 focus:outline-none disabled:opacity-50 resize-none text-base leading-relaxed",onKeyDown:e=>{"Enter"===e.key&&!e.shiftKey&&(e.preventDefault(),(C.trim()||B.length>0)&&N&&!O&&eZ())},style:{minHeight:"24px",maxHeight:"120px"},onInput:e=>{let t=e.target;t.style.height="auto",t.style.height=Math.min(t.scrollHeight,120)+"px"}})}),(0,r.jsx)("button",{type:"submit",disabled:!N||O||!C.trim()&&0===B.length,className:"p-2.5 bg-orange-500 hover:bg-orange-600 disabled:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-xl transition-all duration-200 flex items-center justify-center shadow-lg hover:shadow-xl flex-shrink-0","aria-label":"Send message",title:"Send message",children:O?(0,r.jsx)("svg",{className:"w-5 h-5 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}):(0,r.jsx)(i,{className:"w-5 h-5"})})]})})]})]})})})]}),(0,r.jsx)("div",{className:"fixed top-0 right-0 h-full bg-white border-l border-gray-200/50 shadow-xl transition-all duration-300 ease-in-out z-30 ".concat(V?"w-0 overflow-hidden":"w-80"),style:{transform:V?"translateX(100%)":"translateX(0)",opacity:+!V},children:(0,r.jsxs)("div",{className:"flex flex-col h-full",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200/50",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-orange-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"font-semibold text-gray-900",children:"History"}),(0,r.jsxs)("p",{className:"text-xs text-gray-500",children:[ev.length," conversations"]})]})]}),(0,r.jsx)("button",{onClick:()=>J(!V),className:"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200 hover:scale-105","aria-label":"Toggle history sidebar",children:(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,r.jsx)("div",{className:"p-4 border-b border-gray-200/50",children:(0,r.jsxs)("button",{onClick:eD,className:"w-full flex items-center justify-center space-x-2 px-4 py-3 bg-orange-500 hover:bg-orange-600 text-white rounded-xl transition-all duration-200 shadow-sm hover:shadow-md",children:[(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),(0,r.jsx)("span",{className:"font-medium",children:"New Chat"})]})}),(0,r.jsx)("div",{className:"flex-1 overflow-y-auto p-4 space-y-2",children:ew?(0,r.jsx)("div",{className:"space-y-2 p-4",children:Array.from({length:8}).map((e,t)=>(0,r.jsxs)("div",{className:"p-3 rounded-xl border border-gray-100 animate-pulse",children:[(0,r.jsx)("div",{className:"bg-gray-200 h-4 w-3/4 rounded mb-2"}),(0,r.jsx)("div",{className:"bg-gray-200 h-3 w-1/2 rounded"})]},t))}):0===ev.length?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-3",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"No conversations yet"}),(0,r.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Start chatting to see your history"})]}):(0,r.jsx)(r.Fragment,{children:ev.map(e=>(0,r.jsx)(w,{chat:e,currentConversation:q,onLoadChat:eB,onDeleteChat:eM},e.id))})}),eb&&(0,r.jsx)("div",{className:"px-4 py-2 bg-orange-50 border-t border-orange-100",children:(0,r.jsxs)("div",{className:"flex items-center text-xs text-orange-600",children:[(0,r.jsx)("svg",{className:"w-3 h-3 mr-1 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Updating..."]})}),ej&&(0,r.jsx)("div",{className:"px-4 py-2 bg-red-50 border-t border-red-100",children:(0,r.jsxs)("div",{className:"flex items-center justify-between text-xs text-red-600",children:[(0,r.jsx)("span",{children:"Failed to load history"}),(0,r.jsx)("button",{onClick:()=>eN(!0),className:"text-red-700 hover:text-red-800 font-medium",children:"Retry"})]})})]})}),(0,r.jsx)("div",{className:"fixed top-20 right-4 z-40 transition-all duration-300 ease-in-out ".concat(V?"opacity-100 scale-100 translate-x-0":"opacity-0 scale-95 translate-x-4 pointer-events-none"),children:(0,r.jsx)("button",{onClick:()=>J(!1),className:"p-3 bg-white border border-gray-200/50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 text-gray-600 hover:text-orange-600 hover:scale-105","aria-label":"Show history sidebar",children:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})})})]})}w.displayName="ChatHistoryItem"},82880:(e,t,a)=>{"use strict";a.d(t,{B:()=>n.A,Y:()=>r.A});var r=a(58397),n=a(86474)}},e=>{var t=t=>e(e.s=t);e.O(0,[274,5738,1486,2662,8669,8848,3338,4696,9173,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(24766)),_N_E=e.O()}]);