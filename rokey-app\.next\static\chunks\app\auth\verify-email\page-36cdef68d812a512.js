(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3270],{37881:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>h});var t=r(95155),s=r(12115),i=r(6874),n=r.n(i),l=r(66766),d=r(55020),c=r(5279);let o=s.forwardRef(function(e,a){let{title:r,titleId:t,...i}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},i),r?s.createElement("title",{id:t},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"}))});var x=r(21394),m=r(73579),u=r(35695);function b(){let[e,a]=(0,s.useState)(!1),[r,i]=(0,s.useState)(""),[b,h]=(0,s.useState)(""),g=(0,u.useSearchParams)(),f=(0,u.useRouter)(),p=(0,m.createClientComponentClient)();(0,s.useEffect)(()=>{let e=g.get("email");e&&h(decodeURIComponent(e)),(async()=>{let{data:{session:e}}=await p.auth.getSession();e&&f.push("/dashboard")})()},[g,f,p.auth]);let y=async()=>{if(!b)return void i("Please enter your email address");a(!0),i("");try{let{error:e}=await p.auth.resend({type:"signup",email:b});if(e)throw e;i("Verification email sent! Please check your inbox.")}catch(e){i(e.message||"Failed to resend email. Please try again.")}finally{a(!1)}};return(0,t.jsxs)("div",{className:"min-h-screen bg-white relative overflow-hidden flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:[(0,t.jsx)(x.A,{gridSize:50,opacity:.064,color:"#000000",variant:"subtle",animated:!0,className:"fixed inset-0"}),(0,t.jsx)("div",{className:"absolute inset-0",children:(0,t.jsx)("div",{className:"absolute inset-0",style:{backgroundImage:"radial-gradient(circle, rgba(255, 107, 53, 0.12) 1px, transparent 1px)",backgroundSize:"100px 100px",mask:"radial-gradient(ellipse 70% 70% at center, black 30%, transparent 70%)",WebkitMask:"radial-gradient(ellipse 70% 70% at center, black 30%, transparent 70%)"}})}),(0,t.jsx)("div",{className:"relative z-10 w-full max-w-md mx-auto",children:(0,t.jsxs)(d.PY1.div,{initial:{opacity:0,y:50},animate:{opacity:1,y:0},transition:{duration:.8},className:"text-center",children:[(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsxs)(n(),{href:"/",className:"inline-flex items-center space-x-3 mb-8",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-white rounded-xl flex items-center justify-center shadow-lg border border-gray-100 p-1",children:(0,t.jsx)(l.default,{src:"/roukey_logo.png",alt:"RouKey",width:40,height:40,className:"w-full h-full object-contain",priority:!0})}),(0,t.jsx)("span",{className:"text-3xl font-bold text-black",children:"RouKey"})]})}),(0,t.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl border border-gray-100 p-8 backdrop-blur-sm relative overflow-hidden",children:[(0,t.jsx)("div",{className:"absolute inset-0 opacity-5",style:{backgroundImage:"\n                  linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),\n                  linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)\n                ",backgroundSize:"20px 20px"}}),(0,t.jsxs)("div",{className:"relative z-10",children:[(0,t.jsx)("div",{className:"w-20 h-20 bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-full flex items-center justify-center mx-auto mb-6",children:(0,t.jsx)(o,{className:"w-10 h-10 text-white"})}),(0,t.jsx)("h2",{className:"text-3xl font-bold text-black mb-4",children:"Check Your Email"}),(0,t.jsx)("p",{className:"text-gray-600 text-lg mb-6",children:"We've sent a verification link to:"}),b&&(0,t.jsx)("div",{className:"bg-gray-50 rounded-xl p-4 mb-6",children:(0,t.jsx)("p",{className:"text-[#ff6b35] font-semibold text-lg",children:b})}),(0,t.jsx)("p",{className:"text-gray-600 mb-8",children:"Click the link in the email to verify your account and complete your registration."}),(0,t.jsxs)("div",{className:"space-y-4",children:[!b&&(0,t.jsx)("div",{children:(0,t.jsx)("input",{type:"email",placeholder:"Enter your email address",value:b,onChange:e=>h(e.target.value),className:"w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white mb-4"})}),(0,t.jsx)("button",{onClick:y,disabled:e||!b,className:"w-full bg-white border-2 border-gray-200 text-gray-700 py-3 px-6 rounded-xl font-semibold hover:bg-gray-50 hover:border-gray-300 focus:ring-4 focus:ring-gray-200 focus:ring-offset-2 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center",children:e?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(c.A,{className:"w-5 h-5 mr-2 animate-spin"}),"Sending..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(c.A,{className:"w-5 h-5 mr-2"}),"Resend verification email"]})}),r&&(0,t.jsx)("div",{className:"p-4 rounded-xl ".concat(r.includes("sent")?"bg-green-50 border border-green-200":"bg-red-50 border border-red-200"),children:(0,t.jsx)("p",{className:"text-sm ".concat(r.includes("sent")?"text-green-600":"text-red-600"),children:r})})]}),(0,t.jsx)("div",{className:"mt-8 pt-6 border-t border-gray-200",children:(0,t.jsxs)("p",{className:"text-gray-600",children:["Already verified?"," ",(0,t.jsx)(n(),{href:"/auth/signin",className:"text-[#ff6b35] hover:text-[#e55a2b] font-semibold transition-colors",children:"Sign in to your account"})]})})]})]})]})})]})}function h(){return(0,t.jsx)(s.Suspense,{fallback:(0,t.jsx)("div",{className:"min-h-screen bg-white flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}),children:(0,t.jsx)(b,{})})}},56047:(e,a,r)=>{Promise.resolve().then(r.bind(r,37881))}},e=>{var a=a=>e(e.s=a);e.O(0,[7871,2115,8888,1459,1486,2662,8669,8848,3338,4696,9173,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>a(56047)),_N_E=e.O()}]);