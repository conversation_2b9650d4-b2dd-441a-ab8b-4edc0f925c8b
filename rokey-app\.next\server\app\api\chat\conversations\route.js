(()=>{var e={};e.id=7489,e.ids=[7489],e.modules={2507:(e,t,r)=>{"use strict";r.d(t,{x:()=>o});var s=r(34386),a=r(44999);async function o(){let e=await (0,a.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,r,s){try{e.set({name:t,value:r,...s})}catch(e){}},remove(t,r){try{e.set({name:t,value:"",...r})}catch(e){}}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},21645:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>l,serverHooks:()=>m,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{DELETE:()=>d,GET:()=>c,POST:()=>p});var a=r(96559),o=r(48088),i=r(37719),n=r(32190),u=r(2507);async function c(e){let t=await (0,u.x)(),{searchParams:r}=new URL(e.url),s=r.get("custom_api_config_id");if(!s)return n.NextResponse.json({error:"custom_api_config_id query parameter is required"},{status:400});try{let{data:r,error:a}=await t.from("chat_conversations").select(`
        id,
        custom_api_config_id,
        title,
        created_at,
        updated_at
      `).eq("custom_api_config_id",s).order("updated_at",{ascending:!1}).limit(50);if(a)return n.NextResponse.json({error:"Failed to fetch conversations",details:a.message},{status:500});let o=(r||[]).map(e=>e.id);if(0===o.length){let e=n.NextResponse.json([],{status:200});return e.headers.set("Cache-Control","private, max-age=30"),e}let{data:i,error:u}=await t.from("chat_messages").select("conversation_id, content, created_at, role").in("conversation_id",o).order("created_at",{ascending:!1}).limit(2*o.length),c=new Map;if(i)for(let e of i){let t=e.conversation_id,r=c.get(t);r?(r.count++,r.lastMessage||(r.lastMessage=e)):c.set(t,{count:1,lastMessage:e})}let p=(r||[]).map(e=>{let t=c.get(e.id)||{count:0},r="";if(t.lastMessage?.content&&Array.isArray(t.lastMessage.content)){let e=t.lastMessage.content.filter(e=>"text"===e.type&&e.text).map(e=>e.text).join(" ");r=e.length>100?e.substring(0,100)+"...":e}return{id:e.id,custom_api_config_id:e.custom_api_config_id,title:e.title,created_at:e.created_at,updated_at:e.updated_at,message_count:t.count,last_message_preview:r}}),d=n.NextResponse.json(p,{status:200}),l="true"===e.headers.get("X-Prefetch");return d.headers.set("Cache-Control",`private, max-age=${l?300:30}, stale-while-revalidate=60`),d.headers.set("X-Content-Type-Options","nosniff"),d.headers.set("Vary","X-Prefetch"),d}catch(e){return n.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}async function p(e){let t=await (0,u.x)();try{let{custom_api_config_id:r,title:s}=await e.json();if(!r||!s)return n.NextResponse.json({error:"Missing required fields: custom_api_config_id, title"},{status:400});let{data:a,error:o}=await t.from("chat_conversations").insert({custom_api_config_id:r,title:s}).select().single();if(o)return n.NextResponse.json({error:"Failed to create conversation",details:o.message},{status:500});return n.NextResponse.json(a,{status:201})}catch(e){if("SyntaxError"===e.name)return n.NextResponse.json({error:"Invalid request body: Malformed JSON."},{status:400});return n.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}async function d(e){let t=await (0,u.x)(),{searchParams:r}=new URL(e.url),s=r.get("id");if(!s)return n.NextResponse.json({error:"id query parameter is required"},{status:400});try{let{error:e}=await t.from("chat_conversations").delete().eq("id",s);if(e)return n.NextResponse.json({error:"Failed to delete conversation",details:e.message},{status:500});return n.NextResponse.json({message:"Conversation deleted successfully"},{status:200})}catch(e){return n.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/chat/conversations/route",pathname:"/api/chat/conversations",filename:"route",bundlePath:"app/api/chat/conversations/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\chat\\conversations\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:g,serverHooks:m}=l;function f(){return(0,i.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:g})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,9398,4999,4386],()=>r(21645));module.exports=s})();