import Stripe from 'stripe';

export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-02-24.acacia',
});

export const STRIPE_CONFIG = {
  PRICE_IDS: {
    STARTER: process.env.STRIPE_STARTER_PRICE_ID!,
    PROFESSIONAL: process.env.STRIPE_PROFESSIONAL_PRICE_ID!,
    ENTERPRISE: process.env.STRIPE_ENTERPRISE_PRICE_ID!,
  },
  PRODUCT_IDS: {
    STARTER: process.env.STRIPE_STARTER_PRODUCT_ID!,
    PROFESSIONAL: process.env.STRIPE_PROFESSIONAL_PRODUCT_ID!,
    ENTERPRISE: process.env.STRIPE_ENTERPRISE_PRODUCT_ID!,
  },
};

export type SubscriptionTier = 'starter' | 'professional' | 'enterprise';

export interface TierConfig {
  name: string;
  price: string;
  priceId: string;
  productId: string;
  features: string[];
  limits: {
    configurations: number;
    apiKeysPerConfig: number;
    apiRequests: number;
  };
}

export const TIER_CONFIGS: Record<SubscriptionTier, TierConfig> = {
  starter: {
    name: 'Starter',
    price: '$29',
    priceId: STRIPE_CONFIG.PRICE_IDS.STARTER,
    productId: STRIPE_CONFIG.PRODUCT_IDS.STARTER,
    features: [
      'Up to 3 API configurations',
      'Up to 10 API keys per configuration',
      'Unlimited API requests',
      'Basic routing strategies',
      'Email support',
    ],
    limits: {
      configurations: 3,
      apiKeysPerConfig: 10,
      apiRequests: 999999,
    },
  },
  professional: {
    name: 'Professional',
    price: '$99',
    priceId: STRIPE_CONFIG.PRICE_IDS.PROFESSIONAL,
    productId: STRIPE_CONFIG.PRODUCT_IDS.PROFESSIONAL,
    features: [
      'Up to 15 API configurations',
      'Up to 50 API keys per configuration',
      'Unlimited API requests',
      'Advanced routing strategies',
      'Priority email support',
      'Analytics dashboard',
    ],
    limits: {
      configurations: 15,
      apiKeysPerConfig: 50,
      apiRequests: 999999,
    },
  },
  enterprise: {
    name: 'Enterprise',
    price: '$299',
    priceId: STRIPE_CONFIG.PRICE_IDS.ENTERPRISE,
    productId: STRIPE_CONFIG.PRODUCT_IDS.ENTERPRISE,
    features: [
      'Unlimited API configurations',
      'Unlimited API keys',
      'Unlimited API requests',
      'All routing strategies',
      'Priority phone & email support',
      'Advanced analytics',
      'Custom integrations',
      'SLA guarantee',
    ],
    limits: {
      configurations: 999999,
      apiKeysPerConfig: 999999,
      apiRequests: 999999,
    },
  },
};

export function getTierConfig(tier: SubscriptionTier): TierConfig {
  return TIER_CONFIGS[tier];
}

export function getPriceIdForTier(tier: SubscriptionTier): string {
  return TIER_CONFIGS[tier].priceId;
}

export function getTierFromPriceId(priceId: string): SubscriptionTier {
  for (const [tier, config] of Object.entries(TIER_CONFIGS)) {
    if (config.priceId === priceId) {
      return tier as SubscriptionTier;
    }
  }
  return 'starter'; // Default fallback
}

export function formatPrice(tier: SubscriptionTier): string {
  return TIER_CONFIGS[tier].price;
}

export function canPerformAction(
  tier: SubscriptionTier,
  action: 'create_config' | 'create_api_key',
  currentCount: number
): boolean {
  const limits = TIER_CONFIGS[tier].limits;
  
  switch (action) {
    case 'create_config':
      return currentCount < limits.configurations;
    case 'create_api_key':
      return currentCount < limits.apiKeysPerConfig;
    default:
      return true;
  }
}

export interface SubscriptionStatus {
  hasActiveSubscription: boolean;
  tier: SubscriptionTier;
  status: string | null;
  currentPeriodEnd: string | null;
  cancelAtPeriodEnd: boolean;
}

export interface UsageStatus {
  tier: SubscriptionTier;
  usage: {
    configurations: number;
    apiKeys: number;
    apiRequests: number;
  };
  limits: {
    configurations: number;
    apiKeysPerConfig: number;
    apiRequests: number;
  };
  canCreateConfig: boolean;
  canCreateApiKey: boolean;
}
