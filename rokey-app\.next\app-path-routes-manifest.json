{"/_not-found/page": "/_not-found", "/api/activity/route": "/api/activity", "/api/chat/conversations/route": "/api/chat/conversations", "/api/analytics/summary/route": "/api/analytics/summary", "/api/chat/messages/delete-after-timestamp/route": "/api/chat/messages/delete-after-timestamp", "/api/chat/messages/route": "/api/chat/messages", "/api/chat/messages/update-by-timestamp/route": "/api/chat/messages/update-by-timestamp", "/api/custom-configs/[configId]/default-chat-key/route": "/api/custom-configs/[configId]/default-chat-key", "/api/custom-configs/[configId]/default-key-handler/[apiKeyId]/route": "/api/custom-configs/[configId]/default-key-handler/[apiKeyId]", "/api/custom-configs/[configId]/keys/[apiKeyId]/complexity-assignments/route": "/api/custom-configs/[configId]/keys/[apiKeyId]/complexity-assignments", "/api/custom-configs/[configId]/route": "/api/custom-configs/[configId]", "/api/custom-configs/[configId]/routing/route": "/api/custom-configs/[configId]/routing", "/api/custom-configs/route": "/api/custom-configs", "/api/keys/[apiKeyId]/roles/[roleName]/route": "/api/keys/[apiKeyId]/roles/[roleName]", "/api/keys/[apiKeyId]/roles/route": "/api/keys/[apiKeyId]/roles", "/api/keys/[apiKeyId]/route": "/api/keys/[apiKeyId]", "/api/keys/route": "/api/keys", "/api/logs/route": "/api/logs", "/api/orchestration/process-step/route": "/api/orchestration/process-step", "/api/orchestration/status/[executionId]/route": "/api/orchestration/status/[executionId]", "/api/orchestration/start/route": "/api/orchestration/start", "/api/orchestration/stream/[executionId]/route": "/api/orchestration/stream/[executionId]", "/api/orchestration/synthesis-fallback/[executionId]/route": "/api/orchestration/synthesis-fallback/[executionId]", "/api/orchestration/synthesis-stream-direct/[executionId]/route": "/api/orchestration/synthesis-stream-direct/[executionId]", "/api/orchestration/synthesis-stream/[executionId]/route": "/api/orchestration/synthesis-stream/[executionId]", "/api/playground/route": "/api/playground", "/api/providers/list-models/route": "/api/providers/list-models", "/api/stripe/create-checkout-session/route": "/api/stripe/create-checkout-session", "/api/stripe/customer-portal/route": "/api/stripe/customer-portal", "/api/stripe/subscription-status/route": "/api/stripe/subscription-status", "/api/stripe/webhooks/route": "/api/stripe/webhooks", "/api/system-status/route": "/api/system-status", "/api/training/jobs/route": "/api/training/jobs", "/api/training/jobs/upsert/route": "/api/training/jobs/upsert", "/api/user/custom-roles/[customRoleId]/route": "/api/user/custom-roles/[customRoleId]", "/api/user/custom-roles/route": "/api/user/custom-roles", "/auth/callback/route": "/auth/callback", "/api/v1/chat/completions/route": "/api/v1/chat/completions", "/favicon.ico/route": "/favicon.ico", "/about/page": "/about", "/analytics/page": "/analytics", "/add-keys/page": "/add-keys", "/auth/signin/page": "/auth/signin", "/auth/signup/page": "/auth/signup", "/auth/verify-email/page": "/auth/verify-email", "/features/page": "/features", "/dashboard/page": "/dashboard", "/logs/page": "/logs", "/my-models/page": "/my-models", "/my-models/[configId]/page": "/my-models/[configId]", "/page": "/", "/pricing/page": "/pricing", "/playground/page": "/playground", "/routing-setup/[configId]/page": "/routing-setup/[configId]", "/routing-setup/page": "/routing-setup", "/training/page": "/training"}