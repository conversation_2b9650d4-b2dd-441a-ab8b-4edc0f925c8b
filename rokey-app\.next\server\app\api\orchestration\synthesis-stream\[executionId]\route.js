"use strict";(()=>{var e={};e.id=6487,e.ids=[6487,9704],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},40305:(e,t,r)=>{let s;r.r(t),r.d(t,{patchFetch:()=>w,routeModule:()=>y,serverHooks:()=>x,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>g});var o={};r.r(o),r.d(o,{GET:()=>_});var n=r(96559),a=r(48088),i=r(37719),c=r(32190),l=r(2507),u=r(68811),d=r(5649),p=r(55511),m=r.n(p);async function h(e,t,r,s){let o=await (0,l.x)(),{error:n}=await o.from("synthesis_storage").upsert({synthesis_id:e,conversation_id:t,complete_synthesis:r.join(""),chunks:r,total_chunks:r.length,created_at:new Date().toISOString(),last_access_time:new Date().toISOString()});if(n)throw Error(`Failed to store dynamic chunk: ${n.message}`)}try{s=r(99704).createFirstTokenTrackingStream}catch(e){s=e=>e}async function T(e,t={},r=2,s=300){let o=t.timeout||6e4;try{let r=new AbortController,s=setTimeout(()=>r.abort(),o),n={...t,signal:r.signal},a=await fetch(e,n);return clearTimeout(s),a}catch(o){if(r>0)return await new Promise(e=>setTimeout(e,s)),T(e,t,r-1,2*s);throw Error(`Fetch failed after multiple attempts: ${o.message}`)}}async function _(e,{params:t}){let{executionId:r}=await t;if(!r)return c.NextResponse.json({error:"Execution ID is required"},{status:400});let o=await (0,l.x)();try{let e,t,{data:n,error:a}=await o.from("orchestration_executions").select("*").eq("id",r).single();if(a)return c.NextResponse.json({error:`Orchestration execution not found: ${a.message}`},{status:404});if(!n)return c.NextResponse.json({error:"Orchestration execution not found"},{status:404});let{data:i,error:l}=await o.from("orchestration_steps").select("step_number, role_id, response, prompt").eq("execution_id",r).eq("status","completed").order("step_number",{ascending:!0});if(l)return c.NextResponse.json({error:`Error fetching steps: ${l.message}`},{status:500});if(!i||0===i.length)return c.NextResponse.json({error:"No completed steps found for synthesis"},{status:400});let p=i[0]?.prompt?.split('"')[1]||"user request",_=`You are the final moderator synthesizing the work of multiple AI specialists who collaborated on this request: "${p}"

Here are the outputs from each specialist:

${i.map(e=>`**${e.role_id.toUpperCase()} (Step ${e.step_number}):**
${e.response}

`).join("\n")}

Your task is to:
1. Combine these outputs into a single, cohesive, and complete response
2. Ensure the final result fully addresses the original user request
3. Present it in a clear, well-structured format
4. Include any necessary explanations or instructions for the user

Provide the final, polished response that the user will receive:`,y=process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY;if(!y)return c.NextResponse.json({error:"Classification API key not found"},{status:500});let f=new d.y(y,r);try{if(!(e=await T("https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${y}`,"User-Agent":"RoKey/1.0 (Synthesis)",Origin:"https://rokey.app","Cache-Control":"no-cache"},body:JSON.stringify({model:"gemini-2.0-flash-001",messages:[{role:"user",content:_}],stream:!0,temperature:.3,max_tokens:8e3})})).ok){let t=await e.text().catch(()=>"Could not read error response");return c.NextResponse.json({error:`Synthesis API call failed: ${e.status}, ${t}`},{status:e.status})}}catch(e){return c.NextResponse.json({error:`Synthesis API call exception: ${e}`},{status:500})}let g=e.body.getReader(),x=new TextEncoder,w=new TextDecoder,S="",O="",E=0,k="",N=[],I=`synthesis_${r}`,v=`synthesis_${I}_${Date.now()}`,R={MAX_CHARS:2e4,MIN_CHUNK_SIZE:1e3,SEMANTIC_BREAK_BONUS:500},A=async()=>{if(k.length<R.MIN_CHUNK_SIZE)return;let e=k;for(let t of["\n\n","\n",". ","! ","? "]){let r=k.lastIndexOf(t);if(r>R.MIN_CHUNK_SIZE){e=k.substring(0,r+t.length),k=k.substring(r+t.length);break}}e===k&&(k=""),N.push(e);try{await h(v,I,N,E),E++}catch(e){}},C=new ReadableStream({async start(e){try{for(;;){let{done:t,value:s}=await g.read();if(t){if(k.length>0){N.push(k);try{await h(v,I,N,E)}catch(e){}}try{let{data:t}=await o.from("orchestration_executions").select("status").eq("id",r).single();await o.from("orchestration_executions").update({status:"completed",completed_at:new Date().toISOString(),final_response:S}).eq("id",r);let{data:s}=await o.from("orchestration_executions").select("created_at, status").eq("id",r).single(),n=s?.created_at?Date.now()-new Date(s.created_at).getTime():0,{data:a}=await o.from("orchestration_steps").select("step_number").eq("execution_id",r),i=a?.length||0;(0,u.tl)(r,{id:m().randomUUID(),execution_id:r,type:"synthesis_complete",timestamp:new Date().toISOString(),data:{message:(0,u.re)("synthesis_complete"),result:S,totalSteps:i,totalDuration:n}}),await new Promise(e=>setTimeout(e,1e3)),(0,u.tl)(r,{id:m().randomUUID(),execution_id:r,type:"orchestration_completed",timestamp:new Date().toISOString(),data:{commentary:f.generateLiveCommentary("orchestration_completed",{totalSteps:i}),finalResult:S,totalSteps:i,totalDuration:n}}),e.enqueue(x.encode("event: done\ndata: {}\n\n")),e.enqueue(x.encode("data: [DONE]\n\n"))}catch(e){}e.close();break}for(let e of w.decode(s,{stream:!0}).split("\n"))if(e.startsWith("data: ")&&!e.includes("[DONE]"))try{let t=e.substring(6),s=JSON.parse(t);if(s.choices?.[0]?.delta?.content){let e=s.choices[0].delta.content;if(S+=e,O+=e,k+=e,(0,u.tl)(r,{id:m().randomUUID(),execution_id:r,type:"synthesis_streaming",timestamp:new Date().toISOString(),data:{commentary:"\uD83C\uDFA8 Streaming synthesis response...",partialResult:O,progress:Math.min(.9,O.length/1e3),chunkIndex:E,chunkProgress:k.length}}),k.length>=R.MAX_CHARS-R.SEMANTIC_BREAK_BONUS)try{await A()}catch(e){}}}catch(e){}e.enqueue(s)}}catch(t){e.error(t)}}});try{t=s(C,"Gemini","gemini-2.0-flash-001")}catch(e){t=C}let q=new Headers({"Content-Type":"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive","X-RoKey-Stream-Type":"synthesis","X-RoKey-Execution-ID":r});return setTimeout(async()=>{try{let{data:e}=await o.from("orchestration_executions").select("status").eq("id",r).single();e?.status!=="completed"&&(await o.from("orchestration_executions").update({status:"completed",completed_at:new Date().toISOString(),final_response:"Synthesis timed out. Please check the individual specialist outputs."}).eq("id",r),(0,u.tl)(r,{id:m().randomUUID(),execution_id:r,type:"orchestration_completed",timestamp:new Date().toISOString(),data:{commentary:"⚠️ Synthesis timed out, but specialist outputs are available.",finalResult:"Synthesis timed out. Please check the individual specialist outputs.",totalSteps:0,totalDuration:3e4}}))}catch(e){}},3e4),new Response(t,{headers:q})}catch(e){return c.NextResponse.json({error:"Internal server error"},{status:500})}}let y=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/orchestration/synthesis-stream/[executionId]/route",pathname:"/api/orchestration/synthesis-stream/[executionId]",filename:"route",bundlePath:"app/api/orchestration/synthesis-stream/[executionId]/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\orchestration\\synthesis-stream\\[executionId]\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:f,workUnitAsyncStorage:g,serverHooks:x}=y;function w(){return(0,i.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:g})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},99704:(e,t,r)=>{function s(e,t,r){let s=e.getReader(),o=new TextDecoder;return new TextEncoder,new ReadableStream({async start(e){let t=!1;Date.now();try{for(;;){let{done:r,value:n}=await s.read();if(r){e.close();break}let a=o.decode(n,{stream:!0});if(!t&&a.includes("delta"))try{for(let e of a.split("\n"))if(e.startsWith("data: ")&&!e.includes("[DONE]")){let r=e.substring(6);try{let e=JSON.parse(r);if(e.choices?.[0]?.delta?.content){Date.now(),t=!0;break}}catch(e){}}}catch(e){t||(Date.now(),t=!0)}e.enqueue(n)}}catch(t){e.error(t)}}})}function o(e,t,r){void 0!==r.timeToFirstToken&&(r.timeToFirstToken<500||r.timeToFirstToken<1e3||r.timeToFirstToken),r.totalStreamTime,r.totalTokens,r.averageTokenLatency}function n(e,t){return{provider:e||"unknown",model:t||"unknown"}}function a(e){return Math.ceil(e.length/4)}r.r(t),r.d(t,{PERFORMANCE_THRESHOLDS:()=>i,createFirstTokenTrackingStream:()=>s,estimateTokenCount:()=>a,evaluatePerformance:()=>c,getProviderModelFromContext:()=>n,logStreamingPerformance:()=>o});let i={EXCELLENT_FIRST_TOKEN:500,GOOD_FIRST_TOKEN:1e3,SLOW_FIRST_TOKEN:2e3,EXCELLENT_TOTAL:3e3,GOOD_TOTAL:5e3,SLOW_TOTAL:1e4,TARGET_TOKEN_LATENCY:50};function c(e){let t=e.timeToFirstToken?e.timeToFirstToken<i.EXCELLENT_FIRST_TOKEN?"excellent":e.timeToFirstToken<i.GOOD_FIRST_TOKEN?"good":e.timeToFirstToken<i.SLOW_FIRST_TOKEN?"slow":"very_slow":"very_slow",r=e.totalStreamTime?e.totalStreamTime<i.EXCELLENT_TOTAL?"excellent":e.totalStreamTime<i.GOOD_TOTAL?"good":e.totalStreamTime<i.SLOW_TOTAL?"slow":"very_slow":"very_slow",s=e.averageTokenLatency?e.averageTokenLatency<i.TARGET_TOKEN_LATENCY?"excellent":e.averageTokenLatency<2*i.TARGET_TOKEN_LATENCY?"good":e.averageTokenLatency<4*i.TARGET_TOKEN_LATENCY?"slow":"very_slow":"very_slow",o=["excellent","good","slow","very_slow"],n=[t,r,s].reduce((e,t)=>o.indexOf(t)>o.indexOf(e)?t:e,"excellent");return{firstTokenGrade:t,totalTimeGrade:r,tokenLatencyGrade:s,overallGrade:n}}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,9398,4999,4386,8933],()=>r(40305));module.exports=s})();