(()=>{var e={};e.id=3677,e.ids=[3677],e.modules={2507:(e,t,r)=>{"use strict";r.d(t,{x:()=>i});var s=r(34386),a=r(44999);async function i(){let e=await (0,a.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,r,s){try{e.set({name:t,value:r,...s})}catch(e){}},remove(t,r){try{e.set({name:t,value:"",...r})}catch(e){}}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44048:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>c,serverHooks:()=>l,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{DELETE:()=>p});var a=r(96559),i=r(48088),o=r(37719),n=r(32190),u=r(2507);async function p(e){let t=await (0,u.x)();try{let{conversation_id:r,after_timestamp:s,from_timestamp:a}=await e.json();if(!r)return n.NextResponse.json({error:"conversation_id is required"},{status:400});if(!s&&!a)return n.NextResponse.json({error:"Either after_timestamp or from_timestamp is required"},{status:400});let i=s||a,o=new Date(parseInt(i));if(isNaN(o.getTime()))return n.NextResponse.json({error:"Invalid timestamp format"},{status:400});let u=t.from("chat_messages").delete().eq("conversation_id",r);u=s?u.gt("created_at",o.toISOString()):u.gte("created_at",o.toISOString());let{data:p,error:c,count:d}=await u.select("id");if(c)return n.NextResponse.json({error:"Failed to delete messages",details:c.message},{status:500});let m=p?.length||0;return await t.from("chat_conversations").update({updated_at:new Date().toISOString()}).eq("id",r),n.NextResponse.json({success:!0,deleted_count:m,message:`Deleted ${m} messages ${s?"after":"from"} timestamp ${i}`},{status:200})}catch(e){if("SyntaxError"===e.name)return n.NextResponse.json({error:"Invalid request body: Malformed JSON."},{status:400});return n.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/chat/messages/delete-after-timestamp/route",pathname:"/api/chat/messages/delete-after-timestamp",filename:"route",bundlePath:"app/api/chat/messages/delete-after-timestamp/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\chat\\messages\\delete-after-timestamp\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:m,serverHooks:l}=c;function x(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:m})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,9398,4999,4386],()=>r(44048));module.exports=s})();