import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get('code');
  const redirectTo = requestUrl.searchParams.get('redirectTo');

  if (code) {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    try {
      const { data, error } = await supabase.auth.exchangeCodeForSession(code);

      if (error) {
        console.error('Error exchanging code for session:', error);
        return NextResponse.redirect(new URL('/auth/signin?error=auth_callback_error', request.url));
      }

      // If user just signed up and has a plan in the redirect URL, go to checkout
      if (redirectTo && redirectTo.includes('/pricing?plan=') && redirectTo.includes('checkout=true')) {
        return NextResponse.redirect(new URL(redirectTo, request.url));
      }

      // Check if this is a new user who needs to complete subscription
      if (data.user && data.user.email_confirmed_at) {
        // Check if user has an active subscription
        const { data: profile } = await supabase
          .from('user_profiles')
          .select('subscription_status, subscription_tier')
          .eq('user_id', data.user.id)
          .single();

        // If no subscription or incomplete, redirect to pricing for checkout
        if (!profile || !profile.subscription_status || profile.subscription_status !== 'active') {
          return NextResponse.redirect(new URL('/pricing?checkout=true', request.url));
        }
      }

    } catch (error) {
      console.error('Error exchanging code for session:', error);
      return NextResponse.redirect(new URL('/auth/signin?error=auth_callback_error', request.url));
    }
  }

  // Default redirect
  const finalRedirect = redirectTo || '/dashboard';
  return NextResponse.redirect(new URL(finalRedirect, request.url));
}
