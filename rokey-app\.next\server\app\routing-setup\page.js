(()=>{var e={};e.id=807,e.ids=[807],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12927:(e,t,r)=>{Promise.resolve().then(r.bind(r,54320))},14566:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(43210);let s=n.forwardRef(function({title:e,titleId:t,...r},s){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 12a7.5 7.5 0 0 0 15 0m-15 0a7.5 7.5 0 1 1 15 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077 1.41-.513m14.095-5.13 1.41-.513M5.106 17.785l1.15-.964m11.49-9.642 1.149-.964M7.501 19.795l.75-1.3m7.5-12.99.75-1.3m-6.063 16.658.26-1.477m2.605-14.772.26-1.477m0 17.726-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205 12 12m6.894 5.785-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495"}))})},18536:(e,t,r)=>{Promise.resolve().then(r.bind(r,35291))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},49579:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(43210);let s=n.forwardRef(function({title:e,titleId:t,...r},s){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99"}))})},50942:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(43210);let s=n.forwardRef(function({title:e,titleId:t,...r},s){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}))})},54320:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var n=r(60687),s=r(43210),a=r(85814),i=r.n(a),o=r(14566),l=r(62392),d=r(74461),c=r(50942),u=r(49579);let m=s.forwardRef(function({title:e,titleId:t,...r},n){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"}))});var g=r(60925);function p(){let[e,t]=(0,s.useState)([]),[r,a]=(0,s.useState)(!0),[p,x]=(0,s.useState)(null),{createHoverPrefetch:h}=(0,g.c)();return(0,n.jsxs)("div",{className:"min-h-screen",children:[(0,n.jsxs)("div",{className:"mb-8",children:[(0,n.jsx)("h1",{className:"text-h1 text-gray-900 mb-2",children:"Advanced Routing Setup"}),(0,n.jsx)("p",{className:"text-body text-gray-600 max-w-2xl",children:"Configure intelligent routing strategies for your API configurations with enterprise-grade precision"})]}),(0,n.jsxs)("div",{children:[r&&(0,n.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"w-8 h-8 border-4 border-orange-500/20 border-t-orange-500 rounded-full animate-spin mx-auto mb-3"}),(0,n.jsx)("p",{className:"text-gray-600 text-sm",children:"Loading configurations..."})]})}),p&&(0,n.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-xl p-4 mb-6",children:(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)("div",{className:"w-6 h-6 bg-red-100 rounded-full flex items-center justify-center",children:(0,n.jsx)("svg",{className:"w-3 h-3 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-medium text-red-800 text-sm",children:"Error Loading Configurations"}),(0,n.jsx)("p",{className:"text-red-700 text-xs mt-0.5",children:p})]})]})}),!r&&!p&&0===e.length&&(0,n.jsx)("div",{className:"text-center py-12",children:(0,n.jsxs)("div",{className:"card p-8 max-w-sm mx-auto",children:[(0,n.jsx)("div",{className:"w-12 h-12 bg-orange-50 rounded-full flex items-center justify-center mx-auto mb-4 border border-orange-100",children:(0,n.jsx)(o.A,{className:"w-6 h-6 text-orange-500"})}),(0,n.jsx)("h3",{className:"text-h4 text-gray-900 mb-2",children:"No Configurations Found"}),(0,n.jsx)("p",{className:"text-body-sm text-gray-600 mb-4 leading-relaxed",children:"Create your first Custom API Configuration to start setting up intelligent routing strategies"}),(0,n.jsxs)(i(),{href:"/my-models",className:"btn-primary inline-flex items-center text-sm",children:[(0,n.jsx)(o.A,{className:"w-4 h-4 mr-2"}),"Create Configuration"]})]})}),!r&&!p&&e.length>0&&(0,n.jsx)("div",{className:"grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",children:e.map((e,t)=>{let r=[{bg:"bg-gradient-to-br from-pink-500 to-rose-600",icon:"text-white",text:"text-white"},{bg:"bg-gradient-to-br from-blue-500 to-blue-600",icon:"text-white",text:"text-white"},{bg:"bg-gradient-to-br from-emerald-500 to-green-600",icon:"text-white",text:"text-white"},{bg:"bg-gradient-to-br from-amber-500 to-orange-600",icon:"text-white",text:"text-white"},{bg:"bg-gradient-to-br from-purple-500 to-violet-600",icon:"text-white",text:"text-white"},{bg:"bg-gradient-to-br from-cyan-500 to-teal-600",icon:"text-white",text:"text-white"},{bg:"bg-gradient-to-br from-indigo-500 to-blue-700",icon:"text-white",text:"text-white"},{bg:"bg-gradient-to-br from-red-500 to-pink-600",icon:"text-white",text:"text-white"}],s=r[t%r.length],a=(e=>{switch(e){case"intelligent_role":return l.A;case"complexity_round_robin":return d.A;case"strict_fallback":return c.A;case"auto_optimal":return u.A;default:return o.A}})(e.routing_strategy||"none");return(0,n.jsx)(i(),{href:`/routing-setup/${e.id}?from=routing-setup`,className:"group block transition-all duration-300 hover:scale-[1.02] hover:shadow-xl",...h(e.id),children:(0,n.jsxs)("div",{className:"bg-white rounded-2xl shadow-lg overflow-hidden h-80",children:[(0,n.jsxs)("div",{className:`${s.bg} p-8 h-48 flex flex-col items-center justify-center relative`,children:[(0,n.jsx)("div",{className:"w-20 h-20 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300",children:(0,n.jsx)(a,{className:`w-10 h-10 ${s.icon}`})}),(0,n.jsx)("div",{className:"bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full",children:(0,n.jsx)("span",{className:`text-sm font-bold ${s.text}`,children:"none"!==e.routing_strategy&&e.routing_strategy?"intelligent_role"===e.routing_strategy?"Smart Role":"complexity_round_robin"===e.routing_strategy?"Complexity":"strict_fallback"===e.routing_strategy?"Fallback":e.routing_strategy:"Default"})}),(0,n.jsx)(m,{className:`w-6 h-6 ${s.text} absolute top-4 right-4 group-hover:translate-x-1 transition-transform duration-300`})]}),(0,n.jsxs)("div",{className:"p-6 h-32 flex flex-col justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-bold text-black group-hover:text-gray-800 transition-colors duration-200 line-clamp-2 leading-tight",children:e.name}),(0,n.jsx)("p",{className:"text-sm text-gray-600 mt-2",children:"Advanced routing configuration with intelligent strategies"})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("span",{className:"text-xs text-gray-500 font-medium",children:["Created ",new Date(e.created_at).toLocaleDateString()]}),(0,n.jsx)("button",{className:"px-3 py-1 text-xs font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors duration-200",children:"Configure"})]})]})]})},e.id)})})]})]})}},54984:(e,t,r)=>{Promise.resolve().then(r.bind(r,47417))},55541:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\routing-setup\\page.tsx","default")},60925:(e,t,r)=>{"use strict";r.d(t,{c:()=>a});var n=r(43210);let s={};function a(){let[e,t]=(0,n.useState)({}),r=(0,n.useRef)({}),a=(0,n.useCallback)(e=>{let t=s[e];return!!t&&!(Date.now()-t.timestamp>3e5)&&!t.isLoading},[]),i=(0,n.useCallback)(e=>{let t=s[e];return!t||t.isLoading?null:Date.now()-t.timestamp>3e5?(delete s[e],null):t.data},[]),o=(0,n.useCallback)(async(e,n="medium")=>{if(a(e))return i(e);if(s[e]?.isLoading)return null;r.current[e]&&r.current[e].abort();let o=new AbortController;r.current[e]=o,s[e]={data:{},timestamp:Date.now(),isLoading:!0},t(t=>({...t,[e]:"loading"}));try{"low"===n?await new Promise(e=>setTimeout(e,200)):"medium"===n&&await new Promise(e=>setTimeout(e,50));let[r,a,i]=await Promise.allSettled([fetch(`/api/custom-configs/${e}`,{signal:o.signal}),fetch(`/api/keys?custom_config_id=${e}`,{signal:o.signal}),fetch(`/api/complexity-assignments?custom_config_id=${e}`,{signal:o.signal})]),l=null,d=[],c="none",u={},m=[];"fulfilled"===r.status&&r.value.ok&&(c=(l=await r.value.json()).routing_strategy||"none",u=l.routing_strategy_params||{}),"fulfilled"===a.status&&a.value.ok&&(d=await a.value.json()),"fulfilled"===i.status&&i.value.ok&&(m=await i.value.json());let g={configDetails:l,apiKeys:d,routingStrategy:c,routingParams:u,complexityAssignments:m};return s[e]={data:g,timestamp:Date.now(),isLoading:!1},t(t=>({...t,[e]:"success"})),g}catch(r){if("AbortError"===r.name)return null;return delete s[e],t(t=>({...t,[e]:"error"})),null}finally{delete r.current[e]}},[a,i]),l=(0,n.useCallback)(e=>({onMouseEnter:()=>{a(e)||o(e,"high")}}),[o,a]),d=(0,n.useCallback)(e=>{delete s[e],t(t=>{let r={...t};return delete r[e],r})},[]),c=(0,n.useCallback)(()=>{Object.keys(s).forEach(e=>{delete s[e]}),t({})},[]);return{prefetchRoutingSetupData:o,getCachedData:i,isCached:a,createHoverPrefetch:l,clearCache:d,clearAllCache:c,getStatus:(0,n.useCallback)(t=>e[t]||"idle",[e]),getCacheInfo:(0,n.useCallback)(()=>({cachedConfigs:Object.keys(s),cacheSize:Object.keys(s).length,totalCacheAge:Object.values(s).reduce((e,t)=>e+(Date.now()-t.timestamp),0)/Object.keys(s).length}),[]),prefetchStatus:e}}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71238:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(37413),s=r(47417);function a(){return(0,n.jsx)(s.RoutingSetupSkeleton,{})}},79551:e=>{"use strict";e.exports=require("url")},82663:(e,t,r)=>{Promise.resolve().then(r.bind(r,55541))},83829:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var n=r(65239),s=r(48088),a=r(88170),i=r.n(a),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["routing-setup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,55541)),"C:\\RoKey App\\rokey-app\\src\\app\\routing-setup\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(r.bind(r,71238)),"C:\\RoKey App\\rokey-app\\src\\app\\routing-setup\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\RoKey App\\rokey-app\\src\\app\\routing-setup\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/routing-setup/page",pathname:"/routing-setup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4447,9631,1658,4947,6271,2646],()=>r(83829));module.exports=n})();