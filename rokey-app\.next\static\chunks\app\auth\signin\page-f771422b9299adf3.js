(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4680],{79588:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>b});var t=a(95155),r=a(12115),l=a(6874),i=a.n(l),n=a(66766),c=a(55020),o=a(10184),d=a(48987),x=a(21394),m=a(73579),h=a(35695);function u(){let[e,s]=(0,r.useState)(""),[a,l]=(0,r.useState)(""),[u,b]=(0,r.useState)(!1),[p,g]=(0,r.useState)(!1),[f,j]=(0,r.useState)(""),y=(0,h.useRouter)(),v=(0,h.useSearchParams)(),w=(0,m.createClientComponentClient)();(0,r.useEffect)(()=>{(async()=>{let{data:{session:e}}=await w.auth.getSession();if(e){let e=v.get("redirectTo"),s=v.get("plan");s&&["starter","professional","enterprise"].includes(s)?y.push("/pricing?plan=".concat(s,"&checkout=true")):e?y.push(e):y.push("/dashboard")}})()},[y,v,w.auth]);let N=async s=>{s.preventDefault(),g(!0),j("");try{let{data:s,error:t}=await w.auth.signInWithPassword({email:e,password:a});if(t)throw t;if(s.user){let e=v.get("redirectTo"),s=v.get("plan");s&&["starter","professional","enterprise"].includes(s)?y.push("/pricing?plan=".concat(s,"&checkout=true")):e?y.push(e):y.push("/dashboard")}}catch(e){j(e.message||"Invalid email or password. Please try again.")}finally{g(!1)}},k=async()=>{g(!0);try{let e=v.get("plan"),s=v.get("redirectTo"),a="/dashboard";e&&["starter","professional","enterprise"].includes(e)?a="/pricing?plan=".concat(e,"&checkout=true"):s&&(a=s);let{data:t,error:r}=await w.auth.signInWithOAuth({provider:"google",options:{redirectTo:"".concat(window.location.origin,"/auth/callback?redirectTo=").concat(encodeURIComponent(a))}});if(r)throw r}catch(e){j(e.message||"Failed to sign in with Google. Please try again."),g(!1)}};return(0,t.jsxs)("div",{className:"min-h-screen bg-white relative overflow-hidden flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:[(0,t.jsx)(x.A,{gridSize:50,opacity:.064,color:"#000000",variant:"subtle",animated:!0,className:"fixed inset-0"}),(0,t.jsx)("div",{className:"absolute inset-0",children:(0,t.jsx)("div",{className:"absolute inset-0",style:{backgroundImage:"radial-gradient(circle, rgba(255, 107, 53, 0.12) 1px, transparent 1px)",backgroundSize:"100px 100px",mask:"radial-gradient(ellipse 70% 70% at center, black 30%, transparent 70%)",WebkitMask:"radial-gradient(ellipse 70% 70% at center, black 30%, transparent 70%)"}})}),(0,t.jsx)("div",{className:"relative z-10 w-full max-w-6xl mx-auto",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[(0,t.jsx)(c.PY1.div,{initial:{opacity:0,x:-50},animate:{opacity:1,x:0},transition:{duration:.8},className:"hidden lg:block",children:(0,t.jsxs)("div",{className:"bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-3xl p-12 text-white relative overflow-hidden",children:[(0,t.jsx)("div",{className:"absolute inset-0 opacity-20",style:{backgroundImage:"\n                    linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),\n                    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px)\n                  ",backgroundSize:"30px 30px"}}),(0,t.jsxs)("div",{className:"relative z-10",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mb-6 backdrop-blur-sm",children:(0,t.jsx)(n.default,{src:"/roukey_logo.png",alt:"RouKey",width:48,height:48,className:"w-12 h-12 object-contain",priority:!0})}),(0,t.jsx)("h1",{className:"text-4xl font-bold mb-4",children:"Welcome to RouKey"}),(0,t.jsxs)("p",{className:"text-xl text-white/90 mb-8",children:["Access to ",(0,t.jsx)("span",{className:"font-bold",children:"UNLIMITED"})," AI requests across 300+ models"]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-white rounded-full"}),(0,t.jsx)("span",{className:"text-white/90",children:"Intelligent Role Routing"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-white rounded-full"}),(0,t.jsx)("span",{className:"text-white/90",children:"Enterprise Security"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-white rounded-full"}),(0,t.jsx)("span",{className:"text-white/90",children:"No Request Limits"})]})]})]})]})}),(0,t.jsxs)(c.PY1.div,{initial:{opacity:0,x:50},animate:{opacity:1,x:0},transition:{duration:.8,delay:.2},className:"w-full max-w-md mx-auto lg:mx-0",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsxs)(i(),{href:"/",className:"inline-flex items-center space-x-3 mb-8 justify-center lg:justify-start",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-white rounded-xl flex items-center justify-center shadow-lg border border-gray-100 p-1",children:(0,t.jsx)(n.default,{src:"/roukey_logo.png",alt:"RouKey",width:40,height:40,className:"w-full h-full object-contain",priority:!0})}),(0,t.jsx)("span",{className:"text-3xl font-bold text-black",children:"RouKey"})]}),(0,t.jsx)("h2",{className:"text-4xl font-bold text-black mb-3",children:"Sign In"}),(0,t.jsx)("p",{className:"text-gray-600 text-lg",children:"Welcome back to your AI gateway"})]}),(0,t.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl border border-gray-100 p-8 backdrop-blur-sm relative overflow-hidden",children:[(0,t.jsx)("div",{className:"absolute inset-0 opacity-5",style:{backgroundImage:"\n                    linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),\n                    linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)\n                  ",backgroundSize:"20px 20px"}}),(0,t.jsxs)("div",{className:"relative z-10",children:[f&&(0,t.jsx)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-xl",children:(0,t.jsx)("p",{className:"text-red-600 text-sm",children:f})}),(0,t.jsxs)("form",{onSubmit:N,className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-sm font-semibold text-gray-800 mb-3",children:"\uD83D\uDCE7 Email Address"}),(0,t.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e,onChange:e=>s(e.target.value),className:"w-full px-5 py-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white",placeholder:"Enter your email"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"block text-sm font-semibold text-gray-800 mb-3",children:"\uD83D\uDD12 Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{id:"password",name:"password",type:u?"text":"password",autoComplete:"current-password",required:!0,value:a,onChange:e=>l(e.target.value),className:"w-full px-5 py-4 pr-14 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white",placeholder:"Enter your password"}),(0,t.jsx)("button",{type:"button",onClick:()=>b(!u),className:"absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-[#ff6b35] transition-colors",children:u?(0,t.jsx)(d.A,{className:"h-5 w-5"}):(0,t.jsx)(o.A,{className:"h-5 w-5"})})]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-5 w-5 text-[#ff6b35] focus:ring-[#ff6b35] border-gray-300 rounded-lg"}),(0,t.jsx)("label",{htmlFor:"remember-me",className:"ml-3 block text-sm font-medium text-gray-700",children:"Remember me"})]}),(0,t.jsx)(i(),{href:"/auth/reset-password",className:"text-sm text-[#ff6b35] hover:text-[#e55a2b] font-semibold transition-colors",children:"Forgot password?"})]}),(0,t.jsx)("button",{type:"submit",disabled:p,className:"w-full bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white py-4 px-6 rounded-xl font-bold text-lg hover:from-[#e55a2b] hover:to-[#e8851a] focus:ring-4 focus:ring-[#ff6b35]/30 focus:ring-offset-2 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-[1.02]",children:p?(0,t.jsxs)("div",{className:"flex items-center justify-center",children:[(0,t.jsx)("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Signing in..."]}):"Sign In"})]}),(0,t.jsx)("div",{className:"mt-8",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,t.jsx)("div",{className:"w-full border-t border-gray-200"})}),(0,t.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,t.jsx)("span",{className:"px-4 bg-white text-gray-500 font-medium",children:"Or continue with"})})]})}),(0,t.jsxs)("button",{onClick:k,disabled:p,className:"mt-6 w-full bg-white border-2 border-gray-200 text-gray-700 py-4 px-6 rounded-xl font-semibold hover:bg-gray-50 hover:border-gray-300 focus:ring-4 focus:ring-gray-200 focus:ring-offset-2 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center shadow-sm hover:shadow-md",children:[(0,t.jsxs)("svg",{className:"w-6 h-6 mr-3",viewBox:"0 0 24 24",children:[(0,t.jsx)("path",{fill:"#4285F4",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,t.jsx)("path",{fill:"#34A853",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,t.jsx)("path",{fill:"#FBBC05",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,t.jsx)("path",{fill:"#EA4335",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"Continue with Google"]})]})]}),(0,t.jsx)("div",{className:"text-center mt-8",children:(0,t.jsxs)("p",{className:"text-gray-600 text-lg",children:["Don't have an account?"," ",(0,t.jsx)(i(),{href:"/auth/signup",className:"text-[#ff6b35] hover:text-[#e55a2b] font-bold transition-colors",children:"Sign up for free"})]})})]})]})})]})}function b(){return(0,t.jsx)(r.Suspense,{fallback:(0,t.jsx)("div",{className:"min-h-screen bg-white flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}),children:(0,t.jsx)(u,{})})}},96141:(e,s,a)=>{Promise.resolve().then(a.bind(a,79588))}},e=>{var s=s=>e(e.s=s);e.O(0,[7871,2115,8888,1459,1486,2662,8669,8848,3338,4696,9173,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>s(96141)),_N_E=e.O()}]);