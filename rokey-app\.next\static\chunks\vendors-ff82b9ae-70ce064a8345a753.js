"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3338],{92206:(e,t,r)=>{r.d(t,{r0:()=>a});var a={};r.r(a),r.d(a,{decode:()=>C,encode:()=>v});let s=crypto,n=new TextEncoder,c=new TextDecoder,o=e=>{let t=e;"string"==typeof t&&(t=n.encode(t));let r=[];for(let e=0;e<t.length;e+=32768)r.push(String.fromCharCode.apply(null,t.subarray(e,e+32768)));return btoa(r.join(""))},i=e=>{let t=atob(e),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r},l=e=>{let t=e;t instanceof Uint8Array&&(t=c.decode(t)),t=t.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{return i(t)}catch(e){throw TypeError("The input to be decoded is not correctly encoded.")}};class u extends Error{static get code(){return"ERR_JOSE_GENERIC"}constructor(e){var t;super(e),this.code="ERR_JOSE_GENERIC",this.name=this.constructor.name,null==(t=Error.captureStackTrace)||t.call(Error,this,this.constructor)}}class d extends u{constructor(){super(...arguments),this.code="ERR_JOSE_NOT_SUPPORTED"}static get code(){return"ERR_JOSE_NOT_SUPPORTED"}}class S extends u{constructor(){super(...arguments),this.code="ERR_JWKS_INVALID"}static get code(){return"ERR_JWKS_INVALID"}}class y extends u{constructor(){super(...arguments),this.code="ERR_JWKS_NO_MATCHING_KEY",this.message="no applicable key found in the JSON Web Key Set"}static get code(){return"ERR_JWKS_NO_MATCHING_KEY"}}class h extends u{constructor(){super(...arguments),this.code="ERR_JWKS_MULTIPLE_MATCHING_KEYS",this.message="multiple matching keys found in the JSON Web Key Set"}static get code(){return"ERR_JWKS_MULTIPLE_MATCHING_KEYS"}}Symbol.asyncIterator;class p extends u{constructor(){super(...arguments),this.code="ERR_JWKS_TIMEOUT",this.message="request timed out"}static get code(){return"ERR_JWKS_TIMEOUT"}}s.getRandomValues.bind(s);function E(e){if("object"!=typeof e||null===e||"[object Object]"!==Object.prototype.toString.call(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}let f=async e=>{var t,r;if(!e.alg)throw TypeError('"alg" argument is required when "jwk.alg" is not present');let{algorithm:a,keyUsages:n}=function(e){let t,r;switch(e.kty){case"oct":switch(e.alg){case"HS256":case"HS384":case"HS512":t={name:"HMAC",hash:`SHA-${e.alg.slice(-3)}`},r=["sign","verify"];break;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":throw new d(`${e.alg} keys cannot be imported as CryptoKey instances`);case"A128GCM":case"A192GCM":case"A256GCM":case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":t={name:"AES-GCM"},r=["encrypt","decrypt"];break;case"A128KW":case"A192KW":case"A256KW":t={name:"AES-KW"},r=["wrapKey","unwrapKey"];break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":t={name:"PBKDF2"},r=["deriveBits"];break;default:throw new d('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"RSA":switch(e.alg){case"PS256":case"PS384":case"PS512":t={name:"RSA-PSS",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RS256":case"RS384":case"RS512":t={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":t={name:"RSA-OAEP",hash:`SHA-${parseInt(e.alg.slice(-3),10)||1}`},r=e.d?["decrypt","unwrapKey"]:["encrypt","wrapKey"];break;default:throw new d('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"EC":switch(e.alg){case"ES256":t={name:"ECDSA",namedCurve:"P-256"},r=e.d?["sign"]:["verify"];break;case"ES384":t={name:"ECDSA",namedCurve:"P-384"},r=e.d?["sign"]:["verify"];break;case"ES512":t={name:"ECDSA",namedCurve:"P-521"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:"ECDH",namedCurve:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new d('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"OKP":switch(e.alg){case"EdDSA":t={name:e.crv},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new d('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;default:throw new d('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}return{algorithm:t,keyUsages:r}}(e),c=[a,null!=(t=e.ext)&&t,null!=(r=e.key_ops)?r:n];if("PBKDF2"===a.name)return s.subtle.importKey("raw",l(e.k),...c);let o={...e};return delete o.alg,delete o.use,s.subtle.importKey("jwk",o,...c)};async function g(e,t,r){var a;if(!E(e))throw TypeError("JWK must be an object");switch(t||(t=e.alg),e.kty){case"oct":if("string"!=typeof e.k||!e.k)throw TypeError('missing "k" (Key Value) Parameter value');if(null!=r||(r=!0!==e.ext),r)return f({...e,alg:t,ext:null!=(a=e.ext)&&a});return l(e.k);case"RSA":if(void 0!==e.oth)throw new d('RSA JWK "oth" (Other Primes Info) Parameter value is not supported');case"EC":case"OKP":return f({...e,alg:t});default:throw new d('Unsupported "kty" (Key Type) Parameter value')}}Symbol();let A=(e,t)=>{if("string"!=typeof e||!e)throw new JWKInvalid(`${t} missing or invalid`)};function w(e){return e&&"object"==typeof e&&Array.isArray(e.keys)&&e.keys.every(K)}function K(e){return E(e)}class m{constructor(e){if(this._cached=new WeakMap,!w(e))throw new S("JSON Web Key Set malformed");this._jwks=function(e){return"function"==typeof structuredClone?structuredClone(e):JSON.parse(JSON.stringify(e))}(e)}async getKey(e,t){let{alg:r,kid:a}={...e,...null==t?void 0:t.header},s=function(e){switch("string"==typeof e&&e.slice(0,2)){case"RS":case"PS":return"RSA";case"ES":return"EC";case"Ed":return"OKP";default:throw new d('Unsupported "alg" value for a JSON Web Key Set')}}(r),n=this._jwks.keys.filter(e=>{let t=s===e.kty;if(t&&"string"==typeof a&&(t=a===e.kid),t&&"string"==typeof e.alg&&(t=r===e.alg),t&&"string"==typeof e.use&&(t="sig"===e.use),t&&Array.isArray(e.key_ops)&&(t=e.key_ops.includes("verify")),t&&"EdDSA"===r&&(t="Ed25519"===e.crv||"Ed448"===e.crv),t)switch(r){case"ES256":t="P-256"===e.crv;break;case"ES256K":t="secp256k1"===e.crv;break;case"ES384":t="P-384"===e.crv;break;case"ES512":t="P-521"===e.crv}return t}),{0:c,length:o}=n;if(0===o)throw new y;if(1!==o){let e=new h,{_cached:t}=this;throw e[Symbol.asyncIterator]=async function*(){for(let e of n)try{yield await b(t,e,r)}catch(e){continue}},e}return b(this._cached,c,r)}}async function b(e,t,r){let a=e.get(t)||e.set(t,{}).get(t);if(void 0===a[r]){let e=await g({...t,ext:!0},r);if(e instanceof Uint8Array||"public"!==e.type)throw new S("JSON Web Key Set members must be public keys");a[r]=e}return a[r]}let k=async(e,t,r)=>{let a,s,n=!1;"function"==typeof AbortController&&(a=new AbortController,s=setTimeout(()=>{n=!0,a.abort()},t));let c=await fetch(e.href,{signal:a?a.signal:void 0,redirect:"manual",headers:r.headers}).catch(e=>{if(n)throw new p;throw e});if(void 0!==s&&clearTimeout(s),200!==c.status)throw new u("Expected 200 OK from the JSON Web Key Set HTTP response");try{return await c.json()}catch(e){throw new u("Failed to parse the JSON Web Key Set HTTP response as JSON")}},v=e=>o(e).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_"),C=l}}]);