(()=>{var e={};e.id=7158,e.ids=[7158],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},41259:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>h,serverHooks:()=>E,workAsyncStorage:()=>q,workUnitAsyncStorage:()=>v});var s={};r.r(s),r.d(s,{POST:()=>_});var i=r(96559),a=r(48088),o=r(37719),n=r(32190),u=r(64745),c=r(39398);let p=new u.A(process.env.STRIPE_SECRET_KEY,{apiVersion:"2024-12-18.acacia"}),d=(0,c.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY);async function _(e){let t,r=await e.text(),s=e.headers.get("stripe-signature");if(!s)return n.NextResponse.json({error:"Missing signature"},{status:400});try{t=p.webhooks.constructEvent(r,s,process.env.STRIPE_WEBHOOK_SECRET)}catch(e){return n.NextResponse.json({error:"Invalid signature"},{status:400})}try{switch(t.type){case"checkout.session.completed":await l(t.data.object);break;case"customer.subscription.created":await b(t.data.object);break;case"customer.subscription.updated":await f(t.data.object);break;case"customer.subscription.deleted":await w(t.data.object);break;case"invoice.payment_succeeded":await x(t.data.object);break;case"invoice.payment_failed":await m(t.data.object)}return n.NextResponse.json({received:!0})}catch(e){return n.NextResponse.json({error:"Webhook processing failed"},{status:500})}}async function l(e){if(!e.customer||!e.subscription)return;let t=await p.subscriptions.retrieve(e.subscription),r=e.metadata?.user_id;if(!r)return;let s=S(t.items.data[0]?.price.id),{error:i}=await d.from("subscriptions").insert({user_id:r,stripe_subscription_id:t.id,stripe_customer_id:t.customer,tier:s,status:t.status,current_period_start:new Date(1e3*t.current_period_start).toISOString(),current_period_end:new Date(1e3*t.current_period_end).toISOString(),cancel_at_period_end:t.cancel_at_period_end});if(i)throw i;await d.from("user_profiles").update({subscription_tier:s}).eq("id",r)}async function b(e){}async function f(e){let t=S(e.items.data[0]?.price.id),{error:r}=await d.from("subscriptions").update({tier:t,status:e.status,current_period_start:new Date(1e3*e.current_period_start).toISOString(),current_period_end:new Date(1e3*e.current_period_end).toISOString(),cancel_at_period_end:e.cancel_at_period_end,updated_at:new Date().toISOString()}).eq("stripe_subscription_id",e.id);if(r)throw r;let{data:s}=await d.from("subscriptions").select("user_id").eq("stripe_subscription_id",e.id).single();s&&await d.from("user_profiles").update({subscription_tier:t}).eq("id",s.user_id)}async function w(e){let{error:t}=await d.from("subscriptions").update({status:"canceled",updated_at:new Date().toISOString()}).eq("stripe_subscription_id",e.id);if(t)throw t;let{data:r}=await d.from("subscriptions").select("user_id").eq("stripe_subscription_id",e.id).single();r&&await d.from("user_profiles").update({subscription_tier:"starter"}).eq("id",r.user_id)}async function x(e){if(e.subscription){let{error:t}=await d.from("subscriptions").update({status:"active",updated_at:new Date().toISOString()}).eq("stripe_subscription_id",e.subscription)}}async function m(e){if(e.subscription){let{error:t}=await d.from("subscriptions").update({status:"past_due",updated_at:new Date().toISOString()}).eq("stripe_subscription_id",e.subscription)}}function S(e){if(!e)return"starter";switch(e){case process.env.STRIPE_STARTER_PRICE_ID:return"starter";case process.env.STRIPE_PROFESSIONAL_PRICE_ID:return"professional";case process.env.STRIPE_ENTERPRISE_PRICE_ID:return"enterprise";default:return"starter"}}let h=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/stripe/webhooks/route",pathname:"/api/stripe/webhooks",filename:"route",bundlePath:"app/api/stripe/webhooks/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\stripe\\webhooks\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:q,workUnitAsyncStorage:v,serverHooks:E}=h;function g(){return(0,o.patchFetch)({workAsyncStorage:q,workUnitAsyncStorage:v})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,9398,4745],()=>r(41259));module.exports=s})();