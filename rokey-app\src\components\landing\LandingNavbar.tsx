'use client';

import { useState } from 'react';
import Link from 'next/link';
import { prefetcher } from '@/utils/cacheStrategy';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';
import InstantLink from '@/components/ui/InstantLink';
import { useInstantNavigation } from '@/hooks/useInstantNavigation';

export default function LandingNavbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  // Initialize instant navigation
  useInstantNavigation();

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-black/90 backdrop-blur-md border-b border-gray-700">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center p-0.5">
                <Image
                  src="/roukey_logo.png"
                  alt="RouKey"
                  width={28}
                  height={28}
                  className="object-cover"
                />
              </div>
              <span className="text-xl font-bold text-white">RouKey</span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <InstantLink
              href="/features"
              className="text-gray-300 hover:text-white transition-colors duration-100"
            >
              Features
            </InstantLink>
            <InstantLink
              href="/pricing"
              className="text-gray-300 hover:text-white transition-colors duration-100"
            >
              Pricing
            </InstantLink>
            <InstantLink
              href="/about"
              className="text-gray-300 hover:text-white transition-colors duration-100"
            >
              About
            </InstantLink>
            <InstantLink
              href="/auth/signin"
              className="text-gray-300 hover:text-white transition-colors duration-100"
            >
              Sign In
            </InstantLink>
            <InstantLink
              href="/pricing"
              className="bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-6 py-2 rounded-lg hover:shadow-lg hover:shadow-orange-500/25 transition-all duration-100 font-semibold"
            >
              Get Started
            </InstantLink>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-gray-300 hover:text-white transition-colors"
            >
              {isMenuOpen ? (
                <XMarkIcon className="h-6 w-6" />
              ) : (
                <Bars3Icon className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="md:hidden py-4 border-t border-gray-700"
          >
            <div className="flex flex-col space-y-4">
              <InstantLink href="/features" className="text-gray-300 hover:text-white transition-colors duration-100">
                Features
              </InstantLink>
              <InstantLink href="/pricing" className="text-gray-300 hover:text-white transition-colors duration-100">
                Pricing
              </InstantLink>
              <InstantLink href="/about" className="text-gray-300 hover:text-white transition-colors duration-100">
                About
              </InstantLink>
              <InstantLink href="/auth/signin" className="text-gray-300 hover:text-white transition-colors duration-100">
                Sign In
              </InstantLink>
              <InstantLink
                href="/pricing"
                className="bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-4 py-2 rounded-lg hover:shadow-lg hover:shadow-orange-500/25 transition-all duration-100 text-center font-semibold"
              >
                Get Started
              </InstantLink>
            </div>
          </motion.div>
        )}
      </div>
    </nav>
  );
}
