(()=>{var e={};e.id=9890,e.ids=[9890],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},22614:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>E,routeModule:()=>x,serverHooks:()=>m,workAsyncStorage:()=>_,workUnitAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{POST:()=>l});var i=t(96559),o=t(48088),a=t(37719),u=t(32190),n=t(64745),c=t(39398);let p=new n.A(process.env.STRIPE_SECRET_KEY,{apiVersion:"2025-02-24.acacia"}),d=(0,c.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY);async function l(e){try{let r,{priceId:t,userId:s,userEmail:i,tier:o}=await e.json();if(!t||!s||!i||!o)return u.NextResponse.json({error:"Missing required fields: priceId, userId, userEmail, tier"},{status:400});if(!["starter","professional","enterprise"].includes(o))return u.NextResponse.json({error:"Invalid tier. Must be starter, professional, or enterprise"},{status:400});let a=function(e){switch(e){case"starter":return process.env.STRIPE_STARTER_PRICE_ID;case"professional":return process.env.STRIPE_PROFESSIONAL_PRICE_ID;case"enterprise":return process.env.STRIPE_ENTERPRISE_PRICE_ID;default:throw Error(`Invalid tier: ${e}`)}}(o);if(t!==a)return u.NextResponse.json({error:"Price ID does not match selected tier"},{status:400});let{data:n}=await d.from("subscriptions").select("*").eq("user_id",s).eq("status","active").single();if(n)return u.NextResponse.json({error:"User already has an active subscription"},{status:400});let c=await p.customers.list({email:i,limit:1});r=c.data.length>0?c.data[0]:await p.customers.create({email:i,metadata:{user_id:s}});let l=await p.checkout.sessions.create({customer:r.id,payment_method_types:["card"],line_items:[{price:t,quantity:1}],mode:"subscription",success_url:"https://roukey.online/dashboard?session_id={CHECKOUT_SESSION_ID}&success=true",cancel_url:"https://roukey.online/pricing?canceled=true",metadata:{user_id:s,tier:o},subscription_data:{metadata:{user_id:s,tier:o}},allow_promotion_codes:!0,billing_address_collection:"required",customer_update:{address:"auto",name:"auto"}});return u.NextResponse.json({sessionId:l.id,url:l.url})}catch(e){if(e instanceof n.A.errors.StripeError)return u.NextResponse.json({error:`Stripe error: ${e.message}`},{status:400});return u.NextResponse.json({error:"Internal server error"},{status:500})}}let x=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/stripe/create-checkout-session/route",pathname:"/api/stripe/create-checkout-session",filename:"route",bundlePath:"app/api/stripe/create-checkout-session/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\stripe\\create-checkout-session\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:_,workUnitAsyncStorage:h,serverHooks:m}=x;function E(){return(0,a.patchFetch)({workAsyncStorage:_,workUnitAsyncStorage:h})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,9398,4745],()=>t(22614));module.exports=s})();