(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4150],{38152:(e,t,a)=>{"use strict";a.d(t,{Pi:()=>s.A,fK:()=>l.A,uc:()=>r.A});var s=a(55628),r=a(31151),l=a(74500)},44469:(e,t,a)=>{Promise.resolve().then(a.bind(a,76357))},75922:(e,t,a)=>{"use strict";a.d(t,{MG:()=>s});let s=[{id:"openai",name:"OpenAI",apiBaseUrl:"https://api.openai.com/v1/chat/completions",models:[]},{id:"google",name:"Google",apiBaseUrl:"https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",models:[]},{id:"anthropic",name:"Anthropic",apiBaseUrl:"https://api.anthropic.com/v1/chat/completions",models:[]},{id:"deepseek",name:"DeepSeek",apiBaseUrl:"https://api.deepseek.com/chat/completions",models:[]},{id:"xai",name:"xAI (Grok)",apiBaseUrl:"https://api.x.ai/v1/chat/completions",models:[]},{id:"openrouter",name:"OpenRouter",apiBaseUrl:"https://openrouter.ai/api/v1/chat/completions",models:[]}]},76357:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>S});var s=a(95155),r=a(12115),l=a(35695),i=a(75922);let n=[{id:"general_chat",name:"General Chat",description:"Handles general conversation, Q&A, and tasks not covered by other specific roles. Often the default fallback."},{id:"logic_reasoning",name:"Logic & Reasoning",description:"For tasks requiring logical deduction, problem-solving, mathematical reasoning, and complex analytical thinking."},{id:"writing",name:"Writing & Content Creation",description:"For all writing tasks, including articles, blog posts, marketing copy, creative content, essays, and more."},{id:"coding_frontend",name:"Coding - Frontend",description:"For generating and assisting with HTML, CSS, JavaScript, and frontend frameworks (React, Vue, Angular, etc.)."},{id:"coding_backend",name:"Coding - Backend",description:"For generating and assisting with server-side logic, APIs, databases, and backend frameworks (Node.js, Python, Java, etc.)."},{id:"research_synthesis",name:"Research & Synthesis",description:"For information retrieval from various sources, data analysis, and synthesizing findings into reports or summaries."},{id:"summarization_briefing",name:"Summarization & Briefing",description:"For condensing long texts, documents, or conversations into concise summaries or executive briefings."},{id:"translation_localization",name:"Translation & Localization",description:"For translating text between languages and adapting content culturally for different locales."},{id:"data_extraction_structuring",name:"Data Extraction & Structuring",description:"For identifying and extracting specific pieces of information from unstructured/semi-structured text and organizing it."},{id:"brainstorming_ideation",name:"Brainstorming & Ideation",description:"For generating new ideas, exploring concepts, and creative problem-solving sessions."},{id:"education_tutoring",name:"Education & Tutoring",description:"For explaining concepts, answering educational questions, and providing tutoring assistance across various subjects."},{id:"image_generation",name:"Image Generation",description:"For creating images from textual descriptions. Assign to keys linked to image generation models."},{id:"audio_transcription",name:"Audio Transcription",description:"For converting speech from audio files into written text. Assign to keys linked to transcription models."}],o=e=>n.find(t=>t.id===e);var d=a(32461),c=a(6865),m=a(89959),u=a(37186),x=a(67695),h=a(94038),g=a(61316),p=a(85037),f=a(57765),b=a(8246),y=a(31151),v=a(52589),j=a(55424),N=a(80377),w=a(87162),k=a(28003),_=a(79958),C=a(53951),I=a(99323);let A=i.MG.map(e=>({value:e.id,label:e.name}));function S(){var e,t;let a=(0,l.useParams)().configId,S=(0,w.Z)(),T=(0,I.bu)(),D=(null==T?void 0:T.navigateOptimistically)||(e=>{window.location.href=e}),{getCachedData:P,isCached:E}=(0,k._)(),{createHoverPrefetch:F}=(0,C.c)(),[R,M]=(0,r.useState)(null),[O,L]=(0,r.useState)(!0),[K,V]=(0,r.useState)(!1),[B,G]=(0,r.useState)((null==(e=A[0])?void 0:e.value)||"openai"),[U,z]=(0,r.useState)(""),[J,H]=(0,r.useState)(""),[q,W]=(0,r.useState)(""),[Z,Q]=(0,r.useState)(1),[Y,$]=(0,r.useState)(!1),[X,ee]=(0,r.useState)(null),[et,ea]=(0,r.useState)(null),[es,er]=(0,r.useState)(null),[el,ei]=(0,r.useState)(!1),[en,eo]=(0,r.useState)(null),[ed,ec]=(0,r.useState)([]),[em,eu]=(0,r.useState)(!0),[ex,eh]=(0,r.useState)(null),[eg,ep]=(0,r.useState)(null),[ef,eb]=(0,r.useState)(null),[ey,ev]=(0,r.useState)(null),[ej,eN]=(0,r.useState)(1),[ew,ek]=(0,r.useState)(""),[e_,eC]=(0,r.useState)(!1),[eI,eA]=(0,r.useState)([]),[eS,eT]=(0,r.useState)(!1),[eD,eP]=(0,r.useState)(null),[eE,eF]=(0,r.useState)(!1),[eR,eM]=(0,r.useState)(""),[eO,eL]=(0,r.useState)(""),[eK,eV]=(0,r.useState)(""),[eB,eG]=(0,r.useState)(!1),[eU,ez]=(0,r.useState)(null),[eJ,eH]=(0,r.useState)(null),eq=(0,r.useCallback)(async()=>{if(!a)return;let e=P(a);if(e&&e.configDetails){M(e.configDetails),L(!1);return}E(a)||V(!0),L(!0),ee(null);try{let e=await fetch("/api/custom-configs");if(!e.ok){let t=await e.json();throw Error(t.error||"Failed to fetch configurations list")}let t=(await e.json()).find(e=>e.id===a);if(!t)throw Error("Configuration not found in the list.");M(t)}catch(e){ee("Error loading model configuration: ".concat(e.message)),M(null)}finally{L(!1),V(!1)}},[a,P,E]);(0,r.useEffect)(()=>{eq()},[eq]);let eW=(0,r.useCallback)(async()=>{let e=P(a);if(e&&e.models){er(e.models),ei(!1);return}ei(!0),eo(null),er(null);try{let e=await fetch("/api/providers/list-models",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({})}),t=await e.json();if(!e.ok)throw Error(t.error||"Failed to fetch models from database.");t.models?er(t.models):er([])}catch(e){eo("Error fetching models: ".concat(e.message)),er([])}finally{ei(!1)}},[a,P]);(0,r.useEffect)(()=>{a&&eW()},[a,eW]);let eZ=(0,r.useCallback)(async()=>{let e=P(a);if(e&&e.userCustomRoles){eA(e.userCustomRoles),eT(!1);return}eT(!0),eP(null);try{let e=await fetch("/api/user/custom-roles");if(e.ok){let t=await e.json();eA(t)}else{let t;try{t=await e.json()}catch(a){t={error:await e.text().catch(()=>"HTTP error ".concat(e.status))}}let a=t.error||(t.issues?JSON.stringify(t.issues):"Failed to fetch custom roles (status: ".concat(e.status,")"));if(401===e.status)eP(a);else throw Error(a);eA([])}}catch(e){eP(e.message),eA([])}finally{eT(!1)}},[]),eQ=(0,r.useCallback)(async()=>{if(!a||!eI)return;let e=P(a);if(e&&e.apiKeys&&void 0!==e.defaultChatKeyId){let t=e.apiKeys.map(async t=>{let a=await fetch("/api/keys/".concat(t.id,"/roles")),s=[];return a.ok&&(s=(await a.json()).map(e=>{let t=o(e.role_name);if(t)return t;let a=eI.find(t=>t.role_id===e.role_name);return a?{id:a.role_id,name:a.name,description:a.description||void 0}:null}).filter(Boolean)),{...t,assigned_roles:s,is_default_chat_model:e.defaultChatKeyId===t.id}});ec(await Promise.all(t)),ep(e.defaultChatKeyId),eu(!1);return}eu(!0),ee(e=>e&&e.startsWith("Error loading model configuration:")?e:null),ea(null);try{let e=await fetch("/api/keys?custom_config_id=".concat(a));if(!e.ok){let t=await e.json();throw Error(t.error||"Failed to fetch API keys")}let t=await e.json(),s=await fetch("/api/custom-configs/".concat(a,"/default-chat-key"));s.ok;let r=200===s.status?await s.json():null;ep((null==r?void 0:r.id)||null);let l=t.map(async e=>{let t=await fetch("/api/keys/".concat(e.id,"/roles")),a=[];return t.ok&&(a=(await t.json()).map(e=>{let t=o(e.role_name);if(t)return t;let a=eI.find(t=>t.role_id===e.role_name);return a?{id:a.role_id,name:a.name,description:a.description||void 0}:null}).filter(Boolean)),{...e,assigned_roles:a,is_default_chat_model:(null==r?void 0:r.id)===e.id}}),i=await Promise.all(l);ec(i)}catch(e){ee(t=>t?"".concat(t,"; ").concat(e.message):e.message)}finally{eu(!1)}},[a,eI]);(0,r.useEffect)(()=>{R&&eZ()},[R,eZ]),(0,r.useEffect)(()=>{R&&eI&&eQ()},[R,eI,eQ]);let eY=(0,r.useMemo)(()=>{if(es){let e=i.MG.find(e=>e.id===B);if(!e)return[];if("openrouter"===e.id)return es.map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""));if("deepseek"===e.id){let e=[];return es.find(e=>"deepseek-chat"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-chat",label:"Deepseek V3",provider_id:"deepseek"}),es.find(e=>"deepseek-reasoner"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-reasoner",label:"DeepSeek R1-0528",provider_id:"deepseek"}),e.sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return es.filter(t=>t.provider_id===e.id).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return[]},[es,B]),e$=(0,r.useMemo)(()=>{if(es&&ey){let e=i.MG.find(e=>e.id===ey.provider);if(!e)return[];if("openrouter"===e.id)return es.map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""));if("deepseek"===e.id){let e=[];return es.find(e=>"deepseek-chat"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-chat",label:"Deepseek V3",provider_id:"deepseek"}),es.find(e=>"deepseek-reasoner"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-reasoner",label:"DeepSeek R1-0528",provider_id:"deepseek"}),e.sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return es.filter(t=>t.provider_id===e.id).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return[]},[es,ey]);(0,r.useEffect)(()=>{eY.length>0?z(eY[0].value):z("")},[eY,B]),(0,r.useEffect)(()=>{B&&eW()},[B,eW]);let eX=async e=>{if(e.preventDefault(),!a)return void ee("Configuration ID is missing.");if(ed.some(e=>e.predefined_model_id===U))return void ee("This model is already configured in this setup. Each model can only be used once per configuration, but you can use the same API key with different models.");$(!0),ee(null),ea(null);let t=[...ed];try{var s;let e=await fetch("/api/keys",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({custom_api_config_id:a,provider:B,predefined_model_id:U,api_key_raw:J,label:q,temperature:Z})}),t=await e.json();if(!e.ok)throw Error(t.details||t.error||"Failed to save API key");let r={id:t.id,custom_api_config_id:a,provider:B,predefined_model_id:U,label:q,temperature:Z,status:"active",created_at:new Date().toISOString(),last_used_at:null,is_default_chat_model:!1,assigned_roles:[]};ec(e=>[...e,r]),ea('API key "'.concat(q,'" saved successfully!')),G((null==(s=A[0])?void 0:s.value)||"openai"),H(""),W(""),Q(1),eY.length>0&&z(eY[0].value)}catch(e){ec(t),ee("Save Key Error: ".concat(e.message))}finally{$(!1)}},e0=e=>{ev(e),eN(e.temperature||1),ek(e.predefined_model_id)},e2=async()=>{if(!ey)return;if(ed.some(e=>e.id!==ey.id&&e.predefined_model_id===ew))return void ee("This model is already configured in this setup. Each model can only be used once per configuration.");eC(!0),ee(null),ea(null);let e=[...ed];ec(e=>e.map(e=>e.id===ey.id?{...e,temperature:ej,predefined_model_id:ew}:e));try{let t=await fetch("/api/keys?id=".concat(ey.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({temperature:ej,predefined_model_id:ew})}),a=await t.json();if(!t.ok)throw ec(e),Error(a.details||a.error||"Failed to update API key");ea('API key "'.concat(ey.label,'" updated successfully!')),ev(null)}catch(e){ee("Update Key Error: ".concat(e.message))}finally{eC(!1)}},e1=(e,t)=>{S.showConfirmation({title:"Delete API Key",message:'Are you sure you want to delete the API key "'.concat(t,'"? This will permanently remove the key and unassign all its roles. This action cannot be undone.'),confirmText:"Delete API Key",cancelText:"Cancel",type:"danger"},async()=>{eh(e),ee(null),ea(null);let a=[...ed],s=ed.find(t=>t.id===e);ec(t=>t.filter(t=>t.id!==e)),(null==s?void 0:s.is_default_chat_model)&&ep(null);try{let s=await fetch("/api/keys/".concat(e),{method:"DELETE"}),r=await s.json();if(!s.ok){if(ec(a),ep(eg),404===s.status){ec(t=>t.filter(t=>t.id!==e)),ea('API key "'.concat(t,'" was already deleted.'));return}throw Error(r.details||r.error||"Failed to delete API key")}ea('API key "'.concat(t,'" deleted successfully!'))}catch(e){throw ee("Delete Key Error: ".concat(e.message)),e}finally{eh(null)}})},e5=async e=>{if(!a)return;ee(null),ea(null);let t=[...ed];ec(t=>t.map(t=>({...t,is_default_chat_model:t.id===e}))),ep(e);try{let s=await fetch("/api/custom-configs/".concat(a,"/default-key-handler/").concat(e),{method:"PUT"}),r=await s.json();if(!s.ok)throw ec(t.map(e=>({...e}))),ep(eg),Error(r.details||r.error||"Failed to set default chat key");ea(r.message||"Default general chat key updated!")}catch(e){ee("Set Default Error: ".concat(e.message))}},e6=async(e,t,a)=>{ee(null),ea(null);let s="/api/keys/".concat(e.id,"/roles"),r=[...n.map(e=>({...e,isCustom:!1})),...eI.map(e=>({id:e.role_id,name:e.name,description:e.description||void 0,isCustom:!0,databaseId:e.id}))].find(e=>e.id===t)||{id:t,name:t,description:""},l=ed.map(e=>({...e,assigned_roles:[...e.assigned_roles.map(e=>({...e}))]})),i=null;ef&&ef.id===e.id&&(i={...ef,assigned_roles:[...ef.assigned_roles.map(e=>({...e}))]}),ec(s=>s.map(s=>{if(s.id===e.id){let e=a?s.assigned_roles.filter(e=>e.id!==t):[...s.assigned_roles,r];return{...s,assigned_roles:e}}return s})),ef&&ef.id===e.id&&eb(e=>{if(!e)return null;let s=a?e.assigned_roles.filter(e=>e.id!==t):[...e.assigned_roles,r];return{...e,assigned_roles:s}});try{let n;n=a?await fetch("".concat(s,"/").concat(t),{method:"DELETE"}):await fetch(s,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({role_name:t})});let o=await n.json();if(!n.ok){if(ec(l),i)eb(i);else if(ef&&ef.id===e.id){let t=l.find(t=>t.id===e.id);t&&eb(t)}let t=409===n.status&&o.error?o.error:o.details||o.error||(a?"Failed to unassign role":"Failed to assign role");throw Error(t)}ea(o.message||"Role '".concat(r.name,"' ").concat(a?"unassigned":"assigned"," successfully."))}catch(e){ee("Role Update Error: ".concat(e.message))}},e4=async()=>{if(!eR.trim()||eR.trim().length>30||!/^[a-zA-Z0-9_]+$/.test(eR.trim()))return void ez("Role ID is required (max 30 chars, letters, numbers, underscores only).");if(n.some(e=>e.id.toLowerCase()===eR.trim().toLowerCase())||eI.some(e=>e.role_id.toLowerCase()===eR.trim().toLowerCase()))return void ez("This Role ID is already in use (either predefined or as one of your custom roles).");if(!eO.trim())return void ez("Role Name is required.");ez(null),eG(!0);try{let e=await fetch("/api/user/custom-roles",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({role_id:eR.trim(),name:eO.trim(),description:eK.trim()})});if(!e.ok){let t;try{t=await e.json()}catch(s){let a=await e.text().catch(()=>"HTTP status ".concat(e.status));t={error:"Server error, could not parse response.",details:a}}let a=t.error||"Failed to create custom role.";if(t.details)a+=" (Details: ".concat(t.details,")");else if(t.issues){let e=Object.entries(t.issues).map(e=>{let[t,a]=e;return"".concat(t,": ").concat(a.join(", "))}).join("; ");a+=" (Issues: ".concat(e,")")}throw Error(a)}let t=await e.json();eM(""),eL(""),eV(""),eZ(),ea("Custom role '".concat(t.name,"' created successfully! It is now available globally."))}catch(e){ez(e.message)}finally{eG(!1)}},e3=(e,t)=>{e&&S.showConfirmation({title:"Delete Custom Role",message:'Are you sure you want to delete the custom role "'.concat(t,"\"? This will unassign it from all API keys where it's currently used. This action cannot be undone."),confirmText:"Delete Role",cancelText:"Cancel",type:"danger"},async()=>{eH(e),eP(null),ez(null),ea(null);try{let s=await fetch("/api/user/custom-roles/".concat(e),{method:"DELETE"}),r=await s.json();if(!s.ok)throw Error(r.error||"Failed to delete custom role");eA(t=>t.filter(t=>t.id!==e)),ea(r.message||'Global custom role "'.concat(t,'" deleted successfully.')),a&&eQ()}catch(e){throw eP("Error deleting role: ".concat(e.message)),e}finally{eH(null)}})};return K&&!E(a)?(0,s.jsx)(_.A,{}):O&&!R?(0,s.jsx)(_._,{}):(0,s.jsxs)("div",{className:"min-h-screen",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsxs)("button",{onClick:()=>D("/my-models"),className:"text-orange-600 hover:text-orange-700 inline-flex items-center mb-6 transition-colors duration-200 group",children:[(0,s.jsx)(d.A,{className:"h-5 w-5 mr-2 group-hover:-translate-x-1 transition-transform"}),"Back to My API Models"]}),(0,s.jsx)("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 p-8 mb-6",children:(0,s.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6",children:[(0,s.jsx)("div",{className:"flex-1",children:R?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg",children:(0,s.jsx)(u.A,{className:"h-6 w-6 text-white"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:R.name}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Model Configuration"})]})]}),(0,s.jsxs)("div",{className:"flex items-center text-sm text-gray-600 bg-gray-50 px-4 py-2 rounded-xl w-fit",children:[(0,s.jsx)("span",{className:"inline-block w-2 h-2 bg-orange-500 rounded-full mr-2"}),"ID: ",R.id]})]}):X&&!O?(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-red-100 rounded-2xl flex items-center justify-center mr-4",children:(0,s.jsx)(v.A,{className:"h-6 w-6 text-red-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-red-600",children:"Configuration Error"}),(0,s.jsx)("p",{className:"text-red-500 mt-1",children:X.replace("Error loading model configuration: ","")})]})]}):(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gray-100 rounded-2xl flex items-center justify-center mr-4",children:(0,s.jsx)(m.A,{className:"h-6 w-6 text-gray-400 animate-pulse"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Loading Configuration..."}),(0,s.jsx)("p",{className:"text-gray-500 mt-1",children:"Please wait while we fetch your model details"})]})]})}),R&&(0,s.jsxs)("button",{onClick:()=>D("/routing-setup/".concat(a,"?from=model-config")),className:"inline-flex items-center px-6 py-3 text-sm font-semibold rounded-2xl shadow-sm text-white bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 group",...F(a),children:[(0,s.jsx)(u.A,{className:"h-5 w-5 mr-2 group-hover:rotate-90 transition-transform duration-200"}),"Advanced Routing Setup"]})]})}),et&&(0,s.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-2xl p-4 mb-6 animate-slide-in",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(c.A,{className:"h-5 w-5 text-green-600"}),(0,s.jsx)("p",{className:"text-green-800 font-medium",children:et})]})}),X&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-2xl p-4 mb-6 animate-slide-in",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(v.A,{className:"h-5 w-5 text-red-600"}),(0,s.jsx)("p",{className:"text-red-800 font-medium",children:X})]})})]}),R&&(0,s.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-5 gap-8",children:[(0,s.jsx)("div",{className:"xl:col-span-2",children:(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 p-6 sticky top-8",children:[(0,s.jsxs)("div",{className:"flex items-center mb-6",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mr-3 shadow-md",children:(0,s.jsx)(f.A,{className:"h-5 w-5 text-white"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"Add API Key"}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Configure new key"})]})]}),(0,s.jsxs)("form",{onSubmit:eX,className:"space-y-5",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"provider",className:"block text-sm font-medium text-gray-700 mb-2",children:"Provider"}),(0,s.jsx)("select",{id:"provider",value:B,onChange:e=>{G(e.target.value)},className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm",children:A.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"apiKeyRaw",className:"block text-sm font-medium text-gray-700 mb-2",children:"API Key"}),(0,s.jsx)("input",{id:"apiKeyRaw",type:"password",value:J,onChange:e=>H(e.target.value),className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm",placeholder:"Enter your API key"}),el&&null===es&&(0,s.jsxs)("p",{className:"mt-2 text-xs text-orange-600 flex items-center bg-orange-50 p-2 rounded-lg",children:[(0,s.jsx)(m.A,{className:"h-4 w-4 mr-1 animate-pulse"}),"Fetching models..."]}),en&&(0,s.jsx)("p",{className:"mt-2 text-xs text-red-600 bg-red-50 p-2 rounded-lg",children:en})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"predefinedModelId",className:"block text-sm font-medium text-gray-700 mb-2",children:"Model Variant"}),(0,s.jsx)("select",{id:"predefinedModelId",value:U,onChange:e=>z(e.target.value),disabled:!eY.length,className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm disabled:bg-gray-50 disabled:text-gray-500",children:eY.length>0?eY.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value)):(0,s.jsx)("option",{value:"",disabled:!0,children:null===es&&el?"Loading models...":"Select a provider first"})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"label",className:"block text-sm font-medium text-gray-700 mb-2",children:"Label"}),(0,s.jsx)("input",{type:"text",id:"label",value:q,onChange:e=>W(e.target.value),required:!0,className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm",placeholder:"e.g., My OpenAI GPT-4o Key #1"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"temperature",className:"block text-sm font-medium text-gray-700 mb-2",children:["Temperature",(0,s.jsx)("span",{className:"text-xs text-gray-500 ml-1",children:"(0.0 - 2.0)"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("input",{type:"range",id:"temperature",min:"0",max:"2",step:"0.1",value:Z,onChange:e=>Q(parseFloat(e.target.value)),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider-orange"}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-xs text-gray-500",children:"Conservative"}),(0,s.jsx)("div",{className:"flex items-center space-x-2",children:(0,s.jsx)("input",{type:"number",min:"0",max:"2",step:"0.1",value:Z,onChange:e=>Q(Math.min(2,Math.max(0,parseFloat(e.target.value)||0))),className:"w-16 px-2 py-1 text-xs border border-gray-200 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center"})}),(0,s.jsx)("span",{className:"text-xs text-gray-500",children:"Creative"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative"})]})]})]}),(0,s.jsx)("button",{type:"submit",disabled:Y||!U||""===U||!J.trim()||!q.trim(),className:"w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-medium py-3 px-4 rounded-xl transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-none text-sm",children:Y?(0,s.jsxs)("span",{className:"flex items-center justify-center",children:[(0,s.jsx)(m.A,{className:"h-4 w-4 mr-2 animate-pulse"}),"Saving..."]}):(0,s.jsxs)("span",{className:"flex items-center justify-center",children:[(0,s.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Add API Key"]})})]}),(0,s.jsx)("div",{className:"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-xl",children:(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,s.jsx)(x.A,{className:"h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-blue-900 mb-1",children:"Key Configuration Rules"}),(0,s.jsxs)("div",{className:"text-xs text-blue-800 space-y-1",children:[(0,s.jsxs)("p",{children:["✅ ",(0,s.jsx)("strong",{children:"Same API key, different models:"})," Allowed"]}),(0,s.jsxs)("p",{children:["✅ ",(0,s.jsx)("strong",{children:"Different API keys, same model:"})," Allowed"]}),(0,s.jsxs)("p",{children:["❌ ",(0,s.jsx)("strong",{children:"Same model twice:"})," Not allowed in one configuration"]})]})]})]})})]})}),(0,s.jsx)("div",{className:"xl:col-span-3",children:(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 p-6",children:[(0,s.jsxs)("div",{className:"flex items-center mb-6",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-3 shadow-md",children:(0,s.jsx)(h.A,{className:"h-5 w-5 text-white"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"API Keys & Roles"}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Manage existing keys"})]})]}),em&&(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)(m.A,{className:"h-8 w-8 text-gray-400 mx-auto mb-3 animate-pulse"}),(0,s.jsx)("p",{className:"text-gray-600 text-sm",children:"Loading API keys..."})]}),!em&&0===ed.length&&(!X||X&&X.startsWith("Error loading model configuration:"))&&(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3",children:(0,s.jsx)(h.A,{className:"h-6 w-6 text-gray-400"})}),(0,s.jsx)("h3",{className:"text-sm font-semibold text-gray-900 mb-1",children:"No API Keys"}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Add your first key using the form"})]}),!em&&ed.length>0&&(0,s.jsx)("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:ed.map((e,t)=>(0,s.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-xl p-4 hover:shadow-md transition-all duration-200 animate-slide-in",style:{animationDelay:"".concat(50*t,"ms")},children:(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-center mb-2",children:[(0,s.jsx)("h3",{className:"text-sm font-semibold text-gray-900 truncate mr-2",children:e.label}),e.is_default_chat_model&&(0,s.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 flex-shrink-0",children:[(0,s.jsx)(b.A,{className:"h-3 w-3 mr-1"}),"Default"]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsxs)("p",{className:"text-xs text-gray-900 bg-white px-2 py-1 rounded-lg border",children:[e.provider," (",e.predefined_model_id,")"]}),(0,s.jsxs)("p",{className:"text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded-lg border border-orange-200",children:["Temp: ",e.temperature]})]}),(0,s.jsx)("div",{className:"flex flex-wrap gap-1",children:e.assigned_roles.length>0?e.assigned_roles.map(e=>(0,s.jsx)("span",{className:"inline-block whitespace-nowrap rounded-full bg-orange-100 px-2 py-1 text-xs font-medium text-orange-800",children:e.name},e.id)):(0,s.jsx)("span",{className:"text-xs text-gray-500 bg-white px-2 py-1 rounded-lg border",children:"No roles"})})]}),!e.is_default_chat_model&&(0,s.jsxs)("button",{onClick:()=>e5(e.id),className:"text-xs bg-white border border-gray-200 hover:border-gray-300 text-gray-700 hover:text-gray-900 py-1 px-2 rounded-lg mt-2 transition-colors","data-tooltip-id":"tooltip-set-default-".concat(e.id),"data-tooltip-content":"Set as default chat model",children:["Set Default",(0,s.jsx)(j.m_,{id:"tooltip-set-default-".concat(e.id),place:"top"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-1 ml-2 flex-shrink-0",children:[(0,s.jsxs)("button",{onClick:()=>e0(e),disabled:ex===e.id,className:"p-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors disabled:opacity-50","data-tooltip-id":"tooltip-edit-".concat(e.id),"data-tooltip-content":"Edit Model & Settings",children:[(0,s.jsx)(g.A,{className:"h-4 w-4"}),(0,s.jsx)(j.m_,{id:"tooltip-edit-".concat(e.id),place:"top"})]}),(0,s.jsxs)("button",{onClick:()=>eb(e),disabled:ex===e.id,className:"p-2 text-orange-600 hover:text-orange-700 hover:bg-orange-50 rounded-lg transition-colors disabled:opacity-50","data-tooltip-id":"tooltip-roles-".concat(e.id),"data-tooltip-content":"Manage Roles",children:[(0,s.jsx)(u.A,{className:"h-4 w-4"}),(0,s.jsx)(j.m_,{id:"tooltip-roles-".concat(e.id),place:"top"})]}),(0,s.jsxs)("button",{onClick:()=>e1(e.id,e.label),disabled:ex===e.id,className:"p-2 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50","data-tooltip-id":"tooltip-delete-".concat(e.id),"data-tooltip-content":"Delete Key",children:[ex===e.id?(0,s.jsx)(y.A,{className:"h-4 w-4 animate-pulse"}):(0,s.jsx)(y.A,{className:"h-4 w-4"}),(0,s.jsx)(j.m_,{id:"tooltip-delete-".concat(e.id),place:"top"})]})]})]})},e.id))}),!em&&X&&!X.startsWith("Error loading model configuration:")&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-xl p-4",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(v.A,{className:"h-5 w-5 text-red-600"}),(0,s.jsxs)("p",{className:"text-red-800 font-medium text-sm",children:["Could not load API keys/roles: ",X]})]})})]})})]}),ef&&(()=>{if(!ef)return null;let e=[...n.map(e=>({...e,isCustom:!1})),...eI.map(e=>({id:e.role_id,name:e.name,description:e.description||void 0,isCustom:!0,databaseId:e.id}))].sort((e,t)=>e.name.localeCompare(t.name));return(0,s.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:(0,s.jsxs)("div",{className:"card w-full max-w-lg max-h-[90vh] flex flex-col",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-gray-200",children:[(0,s.jsxs)("h2",{className:"text-xl font-semibold text-gray-900",children:["Manage Roles for: ",(0,s.jsx)("span",{className:"text-orange-600",children:ef.label})]}),(0,s.jsx)("button",{onClick:()=>{eb(null),eF(!1),ez(null)},className:"text-gray-500 hover:text-gray-700 hover:bg-gray-100 p-1 rounded-lg transition-colors duration-200",children:(0,s.jsx)(v.A,{className:"h-6 w-6"})})]}),(0,s.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[eD&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3 mb-4",children:(0,s.jsxs)("p",{className:"text-red-800 text-sm",children:["Error with custom roles: ",eD]})}),(0,s.jsx)("div",{className:"flex justify-end mb-4",children:(0,s.jsxs)("button",{onClick:()=>eF(!eE),className:"btn-primary text-sm inline-flex items-center",children:[(0,s.jsx)(p.A,{className:"h-4 w-4 mr-2"}),eE?"Cancel New Role":"Create New Custom Role"]})}),eE&&(0,s.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-4",children:[(0,s.jsx)("h3",{className:"text-md font-medium text-gray-900 mb-3",children:"Create New Custom Role for this Configuration"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"newCustomRoleId",className:"block text-sm font-medium text-gray-700 mb-1",children:"Role ID (short, no spaces, max 30 chars)"}),(0,s.jsx)("input",{type:"text",id:"newCustomRoleId",value:eR,onChange:e=>eM(e.target.value.replace(/\s/g,"")),className:"form-input",maxLength:30,placeholder:"e.g., my_blog_writer"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"newCustomRoleName",className:"block text-sm font-medium text-gray-700 mb-1",children:"Display Name (max 100 chars)"}),(0,s.jsx)("input",{type:"text",id:"newCustomRoleName",value:eO,onChange:e=>eL(e.target.value),className:"form-input",maxLength:100,placeholder:"e.g., My Awesome Blog Writer"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"newCustomRoleDescription",className:"block text-sm font-medium text-gray-700 mb-1",children:"Description (optional, max 500 chars)"}),(0,s.jsx)("textarea",{id:"newCustomRoleDescription",value:eK,onChange:e=>eV(e.target.value),rows:2,className:"form-input",maxLength:500,placeholder:"Optional: Describe what this role is for..."})]}),eU&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:(0,s.jsx)("p",{className:"text-red-800 text-sm",children:eU})}),(0,s.jsx)("button",{onClick:e4,disabled:eB,className:"btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed",children:eB?"Saving Role...":"Save Custom Role"})]})]})]}),(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-700 mb-3",children:"Select roles to assign:"}),(0,s.jsxs)("div",{className:"overflow-y-auto space-y-2",style:{maxHeight:"calc(90vh - 350px)"},children:[eS&&(0,s.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-orange-600"}),(0,s.jsx)("p",{className:"text-gray-600 text-sm ml-2",children:"Loading custom roles..."})]}),e.map(e=>{let t=ef.assigned_roles.some(t=>t.id===e.id);return(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg border transition-all duration-200 ".concat(t?"bg-orange-50 border-orange-200 shadow-sm":"bg-white border-gray-200 hover:border-gray-300 hover:shadow-sm"),children:[(0,s.jsxs)("label",{htmlFor:"role-".concat(e.id),className:"flex items-center cursor-pointer flex-grow",children:[(0,s.jsx)("input",{type:"checkbox",id:"role-".concat(e.id),checked:t,onChange:()=>e6(ef,e.id,t),className:"h-4 w-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500 focus:ring-2 cursor-pointer"}),(0,s.jsx)("span",{className:"ml-3 text-sm font-medium ".concat(t?"text-orange-800":"text-gray-900"),children:e.name}),e.isCustom&&(0,s.jsx)("span",{className:"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800",children:"Custom"})]}),e.isCustom&&e.databaseId&&(0,s.jsx)("button",{onClick:()=>e3(e.databaseId,e.name),disabled:eJ===e.databaseId,className:"p-1.5 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-wait ml-2",title:"Delete this custom role",children:eJ===e.databaseId?(0,s.jsx)(u.A,{className:"h-4 w-4 animate-spin"}):(0,s.jsx)(y.A,{className:"h-4 w-4"})})]},e.id)})]})]}),(0,s.jsx)("div",{className:"p-6 border-t border-gray-200 bg-gray-50 rounded-b-xl",children:(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsx)("button",{onClick:()=>{eb(null),eF(!1),ez(null)},className:"btn-secondary",children:"Done"})})})]})})})(),ey&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:(0,s.jsxs)("div",{className:"card w-full max-w-lg",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-gray-200",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Edit API Key"}),(0,s.jsx)("button",{onClick:()=>ev(null),className:"text-gray-500 hover:text-gray-700 hover:bg-gray-100 p-1 rounded-lg transition-colors duration-200",children:(0,s.jsx)(v.A,{className:"h-6 w-6"})})]}),(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:ey.label}),(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["Current: ",ey.provider," (",ey.predefined_model_id,")"]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Provider"}),(0,s.jsx)("div",{className:"w-full p-2.5 bg-gray-50 border border-gray-300 rounded-md text-gray-700",children:(null==(t=i.MG.find(e=>e.id===ey.provider))?void 0:t.name)||ey.provider}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Provider cannot be changed"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"editModelId",className:"block text-sm font-medium text-gray-700 mb-2",children:"Model"}),(0,s.jsx)("select",{id:"editModelId",value:ew,onChange:e=>ek(e.target.value),disabled:!e$.length,className:"w-full p-2.5 bg-white border border-gray-300 rounded-md text-gray-900 focus:ring-orange-500 focus:border-orange-500 disabled:opacity-50 disabled:bg-gray-100",children:e$.length>0?e$.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value)):(0,s.jsx)("option",{value:"",disabled:!0,children:el?"Loading models...":"No models available"})})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"editTemperature",className:"block text-sm font-medium text-gray-700 mb-2",children:["Temperature: ",ej]}),(0,s.jsx)("input",{type:"range",id:"editTemperature",min:"0",max:"2",step:"0.1",value:ej,onChange:e=>eN(parseFloat(e.target.value)),className:"slider-orange w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"}),(0,s.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[(0,s.jsx)("span",{children:"0.0 (Focused)"}),(0,s.jsx)("span",{children:"1.0 (Balanced)"}),(0,s.jsx)("span",{children:"2.0 (Creative)"})]})]}),(0,s.jsx)("div",{className:"bg-gray-50 rounded-lg p-3",children:(0,s.jsx)("p",{className:"text-xs text-gray-600",children:"You can change the model and temperature settings. Temperature controls randomness in responses. Lower values (0.0-0.3) are more focused and deterministic, while higher values (1.5-2.0) are more creative and varied."})})]})]}),(0,s.jsx)("div",{className:"p-6 border-t border-gray-200 bg-gray-50 rounded-b-xl",children:(0,s.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,s.jsx)("button",{onClick:()=>ev(null),className:"btn-secondary",disabled:e_,children:"Cancel"}),(0,s.jsx)("button",{onClick:e2,disabled:e_,className:"btn-primary",children:e_?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Saving..."]}):"Save Changes"})]})})]})}),!R&&!O&&!X&&(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 text-center py-16 px-8",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6",children:(0,s.jsx)(x.A,{className:"h-8 w-8 text-gray-400"})}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Model Not Found"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mb-8",children:"This API Model configuration could not be found or may have been deleted."}),(0,s.jsxs)("button",{onClick:()=>D("/my-models"),className:"inline-flex items-center px-6 py-3 text-sm font-medium rounded-xl text-white bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5",children:[(0,s.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Return to My API Models"]})]}),(0,s.jsx)(N.A,{isOpen:S.isOpen,onClose:S.hideConfirmation,onConfirm:S.onConfirm,title:S.title,message:S.message,confirmText:S.confirmText,cancelText:S.cancelText,type:S.type,isLoading:S.isLoading}),(0,s.jsx)(j.m_,{id:"global-tooltip"})]})}},99323:(e,t,a)=>{"use strict";a.d(t,{bu:()=>o,i9:()=>n});var s=a(95155),r=a(12115),l=a(35695);let i=(0,r.createContext)(void 0);function n(e){let{children:t}=e,[a,n]=(0,r.useState)(!1),[o,d]=(0,r.useState)(null),[c,m]=(0,r.useState)([]),[u,x]=(0,r.useState)(new Set),[h,g]=(0,r.useState)(!1),p=(0,l.usePathname)(),f=(0,l.useRouter)(),b=(0,r.useRef)(null),y=(0,r.useRef)([]),v=(0,r.useRef)(null),j=(0,r.useRef)(0),N=(0,r.useRef)({}),w=(0,r.useRef)({});(0,r.useEffect)(()=>{g(!0)},[]);let k=(0,r.useCallback)(e=>{},[h]);(0,r.useEffect)(()=>{p&&!c.includes(p)&&(m(e=>[...e,p]),x(e=>new Set([...e,p])))},[p,c]),(0,r.useEffect)(()=>{k("\uD83D\uDD0D [OPTIMISTIC NAV] Route check: target=".concat(o,", current=").concat(p,", navigationId=").concat(v.current)),o&&v.current&&p===o&&(k("✅ [OPTIMISTIC NAV] Navigation completed: ".concat(o," -> ").concat(p)),b.current&&(clearTimeout(b.current),b.current=null),n(!1),d(null),v.current=null,y.current=y.current.filter(e=>e.route!==o))},[p,o,k]),(0,r.useEffect)(()=>{a&&o&&p===o&&(k("\uD83D\uDE80 [OPTIMISTIC NAV] Immediate route match detected, clearing navigation state"),n(!1),d(null),b.current&&(clearTimeout(b.current),b.current=null))},[p,o,a,k]);let _=(0,r.useCallback)(e=>u.has(e),[u]),C=(0,r.useCallback)(()=>{if(0===y.current.length)return;let e=y.current[y.current.length-1];y.current=[e];let{route:t,id:a}=e;k("\uD83D\uDE80 [OPTIMISTIC NAV] Processing navigation to: ".concat(t," (id: ").concat(a,")")),b.current&&(clearTimeout(b.current),b.current=null),v.current=a;let s=_(t);s&&(k("⚡ [OPTIMISTIC NAV] Using cached navigation for: ".concat(t)),setTimeout(()=>{v.current===a&&n(!1)},100));try{f.push(t)}catch(e){k("❌ [OPTIMISTIC NAV] Router.push failed for: ".concat(t,", using fallback")),window.location.href=t;return}b.current=setTimeout(()=>{if(k("⚠️ [OPTIMISTIC NAV] Timeout reached for: ".concat(t," (id: ").concat(a,"), current path: ").concat(p)),v.current===a){k("\uD83D\uDD04 [OPTIMISTIC NAV] Attempting fallback navigation to: ".concat(t));try{window.location.href=t}catch(e){k("❌ [OPTIMISTIC NAV] Fallback navigation failed: ".concat(e))}n(!1),d(null),v.current=null}b.current=null},s?800:3e3)},[f,p,_,k]),I=(0,r.useCallback)(e=>{if(p===e||!h)return;let t=Date.now();if(t-j.current<100&&o===e)return void k("\uD83D\uDD04 [OPTIMISTIC NAV] Debouncing duplicate navigation to: ".concat(e));if(j.current=t,N.current[e]||(N.current[e]=0),N.current[e]++,w.current[e]&&clearTimeout(w.current[e]),w.current[e]=setTimeout(()=>{N.current[e]=0},2e3),N.current[e]>=3){k("\uD83D\uDEA8 [OPTIMISTIC NAV] Force navigation escape hatch for: ".concat(e)),N.current[e]=0,window.location.href=e;return}b.current&&(clearTimeout(b.current),b.current=null),n(!0),d(e);let a="nav_".concat(t,"_").concat(Math.random().toString(36).substr(2,9));y.current=[{route:e,timestamp:t,id:a}],C()},[p,o,C,k,h]),A=(0,r.useCallback)(()=>{b.current&&(clearTimeout(b.current),b.current=null),n(!1),d(null),v.current=null,y.current=[]},[]);return(0,r.useEffect)(()=>{if(!h)return;let e=()=>{!document.hidden&&a&&(k("\uD83D\uDC41️ [OPTIMISTIC NAV] Document visible, checking if navigation should clear"),setTimeout(()=>{o&&p===o&&(k("\uD83D\uDD27 [OPTIMISTIC NAV] Force clearing navigation state"),n(!1),d(null),b.current&&(clearTimeout(b.current),b.current=null))},100))};return document.addEventListener("visibilitychange",e),()=>document.removeEventListener("visibilitychange",e)},[a,o,p,k,h]),(0,r.useEffect)(()=>()=>{b.current&&clearTimeout(b.current)},[]),(0,s.jsx)(i.Provider,{value:{isNavigating:a,targetRoute:o,navigateOptimistically:I,clearNavigation:A,isPageCached:_,navigationHistory:c},children:t})}function o(){return(0,r.useContext)(i)||null}}},e=>{var t=t=>e(e.s=t);e.O(0,[7874,9968,6060,5738,1486,2662,8669,8848,3338,4696,9173,9628,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(44469)),_N_E=e.O()}]);