"use strict";exports.id=4386,exports.ids=[4386],exports.modules={34386:(e,t,r)=>{r.d(t,{createServerClient:()=>y});var i=r(49343);function o(){return"undefined"!=typeof window&&void 0!==window.document}let n={path:"/",sameSite:"lax",httpOnly:!1,maxAge:3456e4},a=/^(.*)[.](0|[1-9][0-9]*)$/;function s(e,t){if(e===t)return!0;let r=e.match(a);return!!r&&r[1]===t}function l(e,t,r){let i=r??3180,o=encodeURIComponent(t);if(o.length<=i)return[{name:e,value:t}];let n=[];for(;o.length>0;){let e=o.slice(0,i),t=e.lastIndexOf("%");t>i-3&&(e=e.slice(0,t));let r="";for(;e.length>0;)try{r=decodeURIComponent(e);break}catch(t){if(t instanceof URIError&&"%"===e.at(-3)&&e.length>3)e=e.slice(0,e.length-3);else throw t}n.push(r),o=o.slice(e.length)}return n.map((t,r)=>({name:`${e}.${r}`,value:t}))}async function c(e,t){let r=await t(e);if(r)return r;let i=[];for(let r=0;;r++){let o=`${e}.${r}`,n=await t(o);if(!n)break;i.push(n)}return i.length>0?i.join(""):null}let u="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),d=" 	\n\r=".split(""),f=(()=>{let e=Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<d.length;t+=1)e[d[t].charCodeAt(0)]=-2;for(let t=0;t<u.length;t+=1)e[u[t].charCodeAt(0)]=t;return e})();function p(e){let t=[],r=0,i=0;if(function(e,t){for(let r=0;r<e.length;r+=1){let i=e.charCodeAt(r);if(i>55295&&i<=56319){let t=(i-55296)*1024&65535;i=(e.charCodeAt(r+1)-56320&65535|t)+65536,r+=1}!function(e,t){if(e<=127)return t(e);if(e<=2047){t(192|e>>6),t(128|63&e);return}if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|63&e);return}if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|63&e);return}throw Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}(i,t)}}(e,e=>{for(r=r<<8|e,i+=8;i>=6;){let e=r>>i-6&63;t.push(u[e]),i-=6}}),i>0)for(r<<=6-i,i=6;i>=6;){let e=r>>i-6&63;t.push(u[e]),i-=6}return t.join("")}function h(e){let t=[],r=e=>{t.push(String.fromCodePoint(e))},i={utf8seq:0,codepoint:0},o=0,n=0;for(let t=0;t<e.length;t+=1){let a=f[e.charCodeAt(t)];if(a>-1)for(o=o<<6|a,n+=6;n>=8;)(function(e,t,r){if(0===t.utf8seq){if(e<=127)return r(e);for(let r=1;r<6;r+=1)if((e>>7-r&1)==0){t.utf8seq=r;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else if(4===t.utf8seq)t.codepoint=7&e;else throw Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&r(t.codepoint)}})(o>>n-8&255,i,r),n-=8;else if(-2===a)continue;else throw Error(`Invalid Base64-URL character "${e.at(t)}" at position ${t}`)}return t.join("")}let m="base64-";async function g({getAll:e,setAll:t,setItems:r,removedItems:i},o){let a=o.cookieEncoding,c=o.cookieOptions??null,u=await e([...r?Object.keys(r):[],...i?Object.keys(i):[]]),d=u?.map(({name:e})=>e)||[],f=Object.keys(i).flatMap(e=>d.filter(t=>s(t,e))),h=Object.keys(r).flatMap(e=>{let t=new Set(d.filter(t=>s(t,e))),i=r[e];"base64url"===a&&(i=m+p(i));let o=l(e,i);return o.forEach(e=>{t.delete(e.name)}),f.push(...t),o}),g={...n,...c,maxAge:0},w={...n,...c,maxAge:n.maxAge};delete g.name,delete w.name,await t([...f.map(e=>({name:e,value:"",options:g})),...h.map(({name:e,value:t})=>({name:e,value:t,options:w}))])}var w=r(39398);function y(e,t,r){if(!e||!t)throw Error(`Your project's URL and Key are required to create a Supabase client!

Check your Supabase project's API settings to find these values

https://supabase.com/dashboard/project/_/settings/api`);let{storage:a,getAll:u,setAll:d,setItems:f,removedItems:y}=function(e,t){let r,a,u=e.cookies??null,d=e.cookieEncoding,f={},w={};if(u)if("get"in u){let e=async e=>{let t=e.flatMap(e=>[e,...Array.from({length:5}).map((t,r)=>`${e}.${r}`)]),r=[];for(let e=0;e<t.length;e+=1){let i=await u.get(t[e]);(i||"string"==typeof i)&&r.push({name:t[e],value:i})}return r};if(r=async t=>await e(t),"set"in u&&"remove"in u)a=async e=>{for(let t=0;t<e.length;t+=1){let{name:r,value:i,options:o}=e[t];i?await u.set(r,i,o):await u.remove(r,o)}};else if(t)a=async()=>{console.warn("@supabase/ssr: createServerClient was configured without set and remove cookie methods, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness. Consider switching to the getAll and setAll cookie methods instead of get, set and remove which are deprecated and can be difficult to use correctly.")};else throw Error("@supabase/ssr: createBrowserClient requires configuring a getAll and setAll cookie method (deprecated: alternatively both get, set and remove can be used)")}else if("getAll"in u)if(r=async()=>await u.getAll(),"setAll"in u)a=u.setAll;else if(t)a=async()=>{console.warn("@supabase/ssr: createServerClient was configured without the setAll cookie method, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness.")};else throw Error("@supabase/ssr: createBrowserClient requires configuring both getAll and setAll cookie methods (deprecated: alternatively both get, set and remove can be used)");else throw Error(`@supabase/ssr: ${t?"createServerClient":"createBrowserClient"} requires configuring getAll and setAll cookie methods (deprecated: alternatively use get, set and remove).${o()?" As this is called in a browser runtime, consider removing the cookies option object to use the document.cookie API automatically.":""}`);else if(!t&&o()){let e=()=>{let e=(0,i.qg)(document.cookie);return Object.keys(e).map(t=>({name:t,value:e[t]??""}))};r=()=>e(),a=e=>{e.forEach(({name:e,value:t,options:r})=>{document.cookie=(0,i.lK)(e,t,r)})}}else if(t)throw Error("@supabase/ssr: createServerClient must be initialized with cookie options that specify getAll and setAll functions (deprecated, not recommended: alternatively use get, set and remove)");else r=()=>[],a=()=>{throw Error("@supabase/ssr: createBrowserClient in non-browser runtimes (including Next.js pre-rendering mode) was not initialized cookie options that specify getAll and setAll functions (deprecated: alternatively use get, set and remove), but they were needed")};return t?{getAll:r,setAll:a,setItems:f,removedItems:w,storage:{isServer:!0,getItem:async e=>{if("string"==typeof f[e])return f[e];if(w[e])return null;let t=await r([e]),i=await c(e,async e=>{let r=t?.find(({name:t})=>t===e)||null;return r?r.value:null});if(!i)return null;let o=i;return"string"==typeof i&&i.startsWith(m)&&(o=h(i.substring(m.length))),o},setItem:async(t,i)=>{t.endsWith("-code-verifier")&&await g({getAll:r,setAll:a,setItems:{[t]:i},removedItems:{}},{cookieOptions:e?.cookieOptions??null,cookieEncoding:d}),f[t]=i,delete w[t]},removeItem:async e=>{delete f[e],w[e]=!0}}}:{getAll:r,setAll:a,setItems:f,removedItems:w,storage:{isServer:!1,getItem:async e=>{let t=await r([e]),i=await c(e,async e=>{let r=t?.find(({name:t})=>t===e)||null;return r?r.value:null});if(!i)return null;let o=i;return i.startsWith(m)&&(o=h(i.substring(m.length))),o},setItem:async(t,i)=>{let o=await r([t]),c=new Set((o?.map(({name:e})=>e)||[]).filter(e=>s(e,t))),u=i;"base64url"===d&&(u=m+p(i));let f=l(t,u);f.forEach(({name:e})=>{c.delete(e)});let h={...n,...e?.cookieOptions,maxAge:0},g={...n,...e?.cookieOptions,maxAge:n.maxAge};delete h.name,delete g.name;let w=[...[...c].map(e=>({name:e,value:"",options:h})),...f.map(({name:e,value:t})=>({name:e,value:t,options:g}))];w.length>0&&await a(w)},removeItem:async t=>{let i=await r([t]),o=(i?.map(({name:e})=>e)||[]).filter(e=>s(e,t)),l={...n,...e?.cookieOptions,maxAge:0};delete l.name,o.length>0&&await a(o.map(e=>({name:e,value:"",options:l})))}}}}({...r,cookieEncoding:r?.cookieEncoding??"base64url"},!0),b=(0,w.createClient)(e,t,{...r,global:{...r?.global,headers:{...r?.global?.headers,"X-Client-Info":"supabase-ssr/0.6.1 createServerClient"}},auth:{...r?.cookieOptions?.name?{storageKey:r.cookieOptions.name}:null,...r?.auth,flowType:"pkce",autoRefreshToken:!1,detectSessionInUrl:!1,persistSession:!0,storage:a}});return b.auth.onAuthStateChange(async e=>{(Object.keys(f).length>0||Object.keys(y).length>0)&&("SIGNED_IN"===e||"TOKEN_REFRESHED"===e||"USER_UPDATED"===e||"PASSWORD_RECOVERY"===e||"SIGNED_OUT"===e||"MFA_CHALLENGE_VERIFIED"===e)&&await g({getAll:u,setAll:d,setItems:f,removedItems:y},{cookieOptions:r?.cookieOptions??null,cookieEncoding:r?.cookieEncoding??"base64url"})}),b}},49343:(e,t)=>{t.qg=function(e,t){let r=new s,i=e.length;if(i<2)return r;let o=t?.decode||u,n=0;do{let t=e.indexOf("=",n);if(-1===t)break;let a=e.indexOf(";",n),s=-1===a?i:a;if(t>s){n=e.lastIndexOf(";",t-1)+1;continue}let u=l(e,n,t),d=c(e,t,u),f=e.slice(u,d);if(void 0===r[f]){let i=l(e,t+1,s),n=c(e,s,i),a=o(e.slice(i,n));r[f]=a}n=s+1}while(n<i);return r},t.lK=function(e,t,s){let l=s?.encode||encodeURIComponent;if(!r.test(e))throw TypeError(`argument name is invalid: ${e}`);let c=l(t);if(!i.test(c))throw TypeError(`argument val is invalid: ${t}`);let u=e+"="+c;if(!s)return u;if(void 0!==s.maxAge){if(!Number.isInteger(s.maxAge))throw TypeError(`option maxAge is invalid: ${s.maxAge}`);u+="; Max-Age="+s.maxAge}if(s.domain){if(!o.test(s.domain))throw TypeError(`option domain is invalid: ${s.domain}`);u+="; Domain="+s.domain}if(s.path){if(!n.test(s.path))throw TypeError(`option path is invalid: ${s.path}`);u+="; Path="+s.path}if(s.expires){var d;if(d=s.expires,"[object Date]"!==a.call(d)||!Number.isFinite(s.expires.valueOf()))throw TypeError(`option expires is invalid: ${s.expires}`);u+="; Expires="+s.expires.toUTCString()}if(s.httpOnly&&(u+="; HttpOnly"),s.secure&&(u+="; Secure"),s.partitioned&&(u+="; Partitioned"),s.priority)switch("string"==typeof s.priority?s.priority.toLowerCase():void 0){case"low":u+="; Priority=Low";break;case"medium":u+="; Priority=Medium";break;case"high":u+="; Priority=High";break;default:throw TypeError(`option priority is invalid: ${s.priority}`)}if(s.sameSite)switch("string"==typeof s.sameSite?s.sameSite.toLowerCase():s.sameSite){case!0:case"strict":u+="; SameSite=Strict";break;case"lax":u+="; SameSite=Lax";break;case"none":u+="; SameSite=None";break;default:throw TypeError(`option sameSite is invalid: ${s.sameSite}`)}return u};let r=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,i=/^[\u0021-\u003A\u003C-\u007E]*$/,o=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,n=/^[\u0020-\u003A\u003D-\u007E]*$/,a=Object.prototype.toString,s=(()=>{let e=function(){};return e.prototype=Object.create(null),e})();function l(e,t,r){do{let r=e.charCodeAt(t);if(32!==r&&9!==r)return t}while(++t<r);return r}function c(e,t,r){for(;t>r;){let r=e.charCodeAt(--t);if(32!==r&&9!==r)return t+1}return r}function u(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}}};