(()=>{var e={};e.id=5015,e.ids=[5015],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56230:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>p,serverHooks:()=>f,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>d});var i={};r.r(i),r.d(i,{GET:()=>c});var o=r(96559),s=r(48088),a=r(37719),n=r(61223),u=r(44999),l=r(32190);async function c(e){let t=new URL(e.url),r=t.searchParams.get("code"),i=t.searchParams.get("redirectTo")||"/dashboard";if(r){let t=(0,u.UL)(),i=(0,n.createRouteHandlerClient)({cookies:()=>t});try{await i.auth.exchangeCodeForSession(r)}catch(t){return l.NextResponse.redirect(new URL("/auth/signin?error=auth_callback_error",e.url))}}return l.NextResponse.redirect(new URL(i,e.url))}let p=new o.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/auth/callback/route",pathname:"/auth/callback",filename:"route",bundlePath:"app/auth/callback/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\auth\\callback\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:h,workUnitAsyncStorage:d,serverHooks:f}=p;function g(){return(0,a.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:d})}},61223:(e,t,r)=>{"use strict";var i,o=Object.defineProperty,s=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,n=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)o(e,r,{get:t[r],enumerable:!0})})(u,{createBrowserSupabaseClient:()=>j,createClientComponentClient:()=>c,createMiddlewareClient:()=>k,createMiddlewareSupabaseClient:()=>A,createPagesBrowserClient:()=>p,createPagesServerClient:()=>g,createRouteHandlerClient:()=>w,createServerActionClient:()=>O,createServerComponentClient:()=>v,createServerSupabaseClient:()=>M}),e.exports=((e,t,r,i)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let r of a(t))n.call(e,r)||void 0===r||o(e,r,{get:()=>t[r],enumerable:!(i=s(t,r))||i.enumerable});return e})(o({},"__esModule",{value:!0}),u);var l=r(74772);function c({supabaseUrl:e="https://hpkzzhpufhbxtxqaugjh.supabase.co",supabaseKey:t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",options:r,cookieOptions:o,isSingleton:s=!0}={}){if(!e||!t)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");let a=()=>{var i;return(0,l.createSupabaseClient)(e,t,{...r,global:{...null==r?void 0:r.global,headers:{...null==(i=null==r?void 0:r.global)?void 0:i.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.10.0"}},auth:{storage:new l.BrowserCookieAuthStorageAdapter(o)}})};if(s){let e=i??a();return"undefined"==typeof window?e:(i||(i=e),i)}return a()}var p=c,h=r(74772),d=r(83110),f=class extends h.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e}getCookie(e){var t,r,i;return(0,d.splitCookiesString)((null==(r=null==(t=this.context.res)?void 0:t.getHeader("set-cookie"))?void 0:r.toString())??"").map(t=>(0,h.parseCookies)(t)[e]).find(e=>!!e)??(null==(i=this.context.req)?void 0:i.cookies[e])}setCookie(e,t){this._setCookie(e,t)}deleteCookie(e){this._setCookie(e,"",{maxAge:0})}_setCookie(e,t,r){var i;let o=(0,d.splitCookiesString)((null==(i=this.context.res.getHeader("set-cookie"))?void 0:i.toString())??"").filter(t=>!(e in(0,h.parseCookies)(t))),s=(0,h.serializeCookie)(e,t,{...this.cookieOptions,...r,httpOnly:!1});this.context.res.setHeader("set-cookie",[...o,s])}};function g(e,{supabaseUrl:t="https://hpkzzhpufhbxtxqaugjh.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",options:i,cookieOptions:o}={}){var s;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,h.createSupabaseClient)(t,r,{...i,global:{...null==i?void 0:i.global,headers:{...null==(s=null==i?void 0:i.global)?void 0:s.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.10.0"}},auth:{storage:new f(e,o)}})}var I=r(74772),m=r(83110),C=class extends I.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e}getCookie(e){var t;let r=(0,m.splitCookiesString)((null==(t=this.context.res.headers.get("set-cookie"))?void 0:t.toString())??"").map(t=>(0,I.parseCookies)(t)[e]).find(e=>!!e);return r||(0,I.parseCookies)(this.context.req.headers.get("cookie")??"")[e]}setCookie(e,t){this._setCookie(e,t)}deleteCookie(e){this._setCookie(e,"",{maxAge:0})}_setCookie(e,t,r){let i=(0,I.serializeCookie)(e,t,{...this.cookieOptions,...r,httpOnly:!1});this.context.res.headers&&this.context.res.headers.append("set-cookie",i)}};function k(e,{supabaseUrl:t="https://hpkzzhpufhbxtxqaugjh.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",options:i,cookieOptions:o}={}){var s;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,I.createSupabaseClient)(t,r,{...i,global:{...null==i?void 0:i.global,headers:{...null==(s=null==i?void 0:i.global)?void 0:s.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.10.0"}},auth:{storage:new C(e,o)}})}var b=r(74772),y=class extends b.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e,this.isServer=!0}getCookie(e){var t;return null==(t=this.context.cookies().get(e))?void 0:t.value}setCookie(e,t){}deleteCookie(e){}};function v(e,{supabaseUrl:t="https://hpkzzhpufhbxtxqaugjh.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",options:i,cookieOptions:o}={}){var s;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,b.createSupabaseClient)(t,r,{...i,global:{...null==i?void 0:i.global,headers:{...null==(s=null==i?void 0:i.global)?void 0:s.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.10.0"}},auth:{storage:new y(e,o)}})}var x=r(74772),S=class extends x.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e}getCookie(e){var t;return null==(t=this.context.cookies().get(e))?void 0:t.value}setCookie(e,t){this.context.cookies().set(e,t,this.cookieOptions)}deleteCookie(e){this.context.cookies().set(e,"",{...this.cookieOptions,maxAge:0})}};function w(e,{supabaseUrl:t="https://hpkzzhpufhbxtxqaugjh.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",options:i,cookieOptions:o}={}){var s;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,x.createSupabaseClient)(t,r,{...i,global:{...null==i?void 0:i.global,headers:{...null==(s=null==i?void 0:i.global)?void 0:s.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.10.0"}},auth:{storage:new S(e,o)}})}var O=w;function j({supabaseUrl:e="https://hpkzzhpufhbxtxqaugjh.supabase.co",supabaseKey:t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",options:r,cookieOptions:i}={}){return console.warn("Please utilize the `createPagesBrowserClient` function instead of the deprecated `createBrowserSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs-pages"),p({supabaseUrl:e,supabaseKey:t,options:r,cookieOptions:i})}function M(e,{supabaseUrl:t="https://hpkzzhpufhbxtxqaugjh.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",options:i,cookieOptions:o}={}){return console.warn("Please utilize the `createPagesServerClient` function instead of the deprecated `createServerSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs-pages"),g(e,{supabaseUrl:t,supabaseKey:r,options:i,cookieOptions:o})}function A(e,{supabaseUrl:t="https://hpkzzhpufhbxtxqaugjh.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",options:i,cookieOptions:o}={}){return console.warn("Please utilize the `createMiddlewareClient` function instead of the deprecated `createMiddlewareSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs#middleware"),k(e,{supabaseUrl:t,supabaseKey:r,options:i,cookieOptions:o})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},74772:(e,t,r)=>{"use strict";r.r(t),r.d(t,{BrowserCookieAuthStorageAdapter:()=>S,CookieAuthStorageAdapter:()=>x,DEFAULT_COOKIE_OPTIONS:()=>y,createSupabaseClient:()=>w,isBrowser:()=>b,parseCookies:()=>O,parseSupabaseCookie:()=>C,serializeCookie:()=>j,stringifySupabaseSession:()=>k});var i=r(79428);new TextEncoder;let o=new TextDecoder;i.Buffer.isEncoding("base64url");let s=e=>i.Buffer.from(function(e){let t=e;return t instanceof Uint8Array&&(t=o.decode(t)),t}(e),"base64");var a=r(39398),n=Object.create,u=Object.defineProperty,l=Object.getOwnPropertyDescriptor,c=Object.getOwnPropertyNames,p=Object.getPrototypeOf,h=Object.prototype.hasOwnProperty,d=(e,t,r,i)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let o of c(t))h.call(e,o)||o===r||u(e,o,{get:()=>t[o],enumerable:!(i=l(t,o))||i.enumerable});return e},f=(e,t,r)=>(r=null!=e?n(p(e)):{},d(!t&&e&&e.__esModule?r:u(r,"default",{value:e,enumerable:!0}),e)),g=((e,t)=>function(){return t||(0,e[c(e)[0]])((t={exports:{}}).exports,t),t.exports})({"../../node_modules/.pnpm/cookie@0.5.0/node_modules/cookie/index.js"(e){e.parse=function(e,t){if("string"!=typeof e)throw TypeError("argument str must be a string");for(var r={},o=(t||{}).decode||i,s=0;s<e.length;){var a=e.indexOf("=",s);if(-1===a)break;var n=e.indexOf(";",s);if(-1===n)n=e.length;else if(n<a){s=e.lastIndexOf(";",a-1)+1;continue}var u=e.slice(s,a).trim();if(void 0===r[u]){var l=e.slice(a+1,n).trim();34===l.charCodeAt(0)&&(l=l.slice(1,-1)),r[u]=function(e,t){try{return t(e)}catch(t){return e}}(l,o)}s=n+1}return r},e.serialize=function(e,i,s){var a=s||{},n=a.encode||o;if("function"!=typeof n)throw TypeError("option encode is invalid");if(!r.test(e))throw TypeError("argument name is invalid");var u=n(i);if(u&&!r.test(u))throw TypeError("argument val is invalid");var l=e+"="+u;if(null!=a.maxAge){var c=a.maxAge-0;if(isNaN(c)||!isFinite(c))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(c)}if(a.domain){if(!r.test(a.domain))throw TypeError("option domain is invalid");l+="; Domain="+a.domain}if(a.path){if(!r.test(a.path))throw TypeError("option path is invalid");l+="; Path="+a.path}if(a.expires){var p,h=a.expires;if(p=h,"[object Date]"!==t.call(p)&&!(p instanceof Date)||isNaN(h.valueOf()))throw TypeError("option expires is invalid");l+="; Expires="+h.toUTCString()}if(a.httpOnly&&(l+="; HttpOnly"),a.secure&&(l+="; Secure"),a.priority)switch("string"==typeof a.priority?a.priority.toLowerCase():a.priority){case"low":l+="; Priority=Low";break;case"medium":l+="; Priority=Medium";break;case"high":l+="; Priority=High";break;default:throw TypeError("option priority is invalid")}if(a.sameSite)switch("string"==typeof a.sameSite?a.sameSite.toLowerCase():a.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var t=Object.prototype.toString,r=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/;function i(e){return -1!==e.indexOf("%")?decodeURIComponent(e):e}function o(e){return encodeURIComponent(e)}}}),I=f(g()),m=f(g());function C(e){if(!e)return null;try{let t=JSON.parse(e);if(!t)return null;if("Object"===t.constructor.name)return t;if("Array"!==t.constructor.name)throw Error(`Unexpected format: ${t.constructor.name}`);let[r,i,o]=t[0].split("."),a=s(i),n=new TextDecoder,{exp:u,sub:l,...c}=JSON.parse(n.decode(a));return{expires_at:u,expires_in:u-Math.round(Date.now()/1e3),token_type:"bearer",access_token:t[0],refresh_token:t[1],provider_token:t[2],provider_refresh_token:t[3],user:{id:l,factors:t[4],...c}}}catch(e){return console.warn("Failed to parse cookie string:",e),null}}function k(e){var t;return JSON.stringify([e.access_token,e.refresh_token,e.provider_token,e.provider_refresh_token,(null==(t=e.user)?void 0:t.factors)??null])}function b(){return"undefined"!=typeof window&&void 0!==window.document}var y={path:"/",sameSite:"lax",maxAge:31536e6},v=RegExp(".{1,3180}","g"),x=class{constructor(e){this.cookieOptions={...y,...e,maxAge:y.maxAge}}getItem(e){let t=this.getCookie(e);if(e.endsWith("-code-verifier")&&t)return t;if(t)return JSON.stringify(C(t));let r=function(e,t=()=>null){let r=[];for(let i=0;;i++){let o=t(`${e}.${i}`);if(!o)break;r.push(o)}return r.length?r.join(""):null}(e,e=>this.getCookie(e));return null!==r?JSON.stringify(C(r)):null}setItem(e,t){if(e.endsWith("-code-verifier"))return void this.setCookie(e,t);(function(e,t,r){if(1===Math.ceil(t.length/((void 0)??3180)))return[{name:e,value:t}];let i=[],o=t.match(v);return null==o||o.forEach((t,r)=>{let o=`${e}.${r}`;i.push({name:o,value:t})}),i})(e,k(JSON.parse(t))).forEach(e=>{this.setCookie(e.name,e.value)})}removeItem(e){this._deleteSingleCookie(e),this._deleteChunkedCookies(e)}_deleteSingleCookie(e){this.getCookie(e)&&this.deleteCookie(e)}_deleteChunkedCookies(e,t=0){for(let r=t;;r++){let t=`${e}.${r}`;if(void 0===this.getCookie(t))break;this.deleteCookie(t)}}},S=class extends x{constructor(e){super(e)}getCookie(e){return b()?(0,I.parse)(document.cookie)[e]:null}setCookie(e,t){if(!b())return null;document.cookie=(0,I.serialize)(e,t,{...this.cookieOptions,httpOnly:!1})}deleteCookie(e){if(!b())return null;document.cookie=(0,I.serialize)(e,"",{...this.cookieOptions,maxAge:0,httpOnly:!1})}};function w(e,t,r){var i;let o=b();return(0,a.createClient)(e,t,{...r,auth:{flowType:"pkce",autoRefreshToken:o,detectSessionInUrl:o,persistSession:!0,storage:r.auth.storage,...(null==(i=r.auth)?void 0:i.storageKey)?{storageKey:r.auth.storageKey}:{}}})}var O=m.parse,j=m.serialize},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83110:e=>{"use strict";var t={decodeValues:!0,map:!1,silent:!1};function r(e){return"string"==typeof e&&!!e.trim()}function i(e,i){var o,s,a,n,u=e.split(";").filter(r),l=(o=u.shift(),s="",a="",(n=o.split("=")).length>1?(s=n.shift(),a=n.join("=")):a=o,{name:s,value:a}),c=l.name,p=l.value;i=i?Object.assign({},t,i):t;try{p=i.decodeValues?decodeURIComponent(p):p}catch(e){console.error("set-cookie-parser encountered an error while decoding a cookie with value '"+p+"'. Set options.decodeValues to false to disable this feature.",e)}var h={name:c,value:p};return u.forEach(function(e){var t=e.split("="),r=t.shift().trimLeft().toLowerCase(),i=t.join("=");"expires"===r?h.expires=new Date(i):"max-age"===r?h.maxAge=parseInt(i,10):"secure"===r?h.secure=!0:"httponly"===r?h.httpOnly=!0:"samesite"===r?h.sameSite=i:"partitioned"===r?h.partitioned=!0:h[r]=i}),h}function o(e,o){if(o=o?Object.assign({},t,o):t,!e)if(!o.map)return[];else return{};if(e.headers)if("function"==typeof e.headers.getSetCookie)e=e.headers.getSetCookie();else if(e.headers["set-cookie"])e=e.headers["set-cookie"];else{var s=e.headers[Object.keys(e.headers).find(function(e){return"set-cookie"===e.toLowerCase()})];s||!e.headers.cookie||o.silent||console.warn("Warning: set-cookie-parser appears to have been called on a request object. It is designed to parse Set-Cookie headers from responses, not Cookie headers from requests. Set the option {silent: true} to suppress this warning."),e=s}return(Array.isArray(e)||(e=[e]),o.map)?e.filter(r).reduce(function(e,t){var r=i(t,o);return e[r.name]=r,e},{}):e.filter(r).map(function(e){return i(e,o)})}e.exports=o,e.exports.parse=o,e.exports.parseString=i,e.exports.splitCookiesString=function(e){if(Array.isArray(e))return e;if("string"!=typeof e)return[];var t,r,i,o,s,a=[],n=0;function u(){for(;n<e.length&&/\s/.test(e.charAt(n));)n+=1;return n<e.length}for(;n<e.length;){for(t=n,s=!1;u();)if(","===(r=e.charAt(n))){for(i=n,n+=1,u(),o=n;n<e.length&&"="!==(r=e.charAt(n))&&";"!==r&&","!==r;)n+=1;n<e.length&&"="===e.charAt(n)?(s=!0,n=o,a.push(e.substring(t,i)),t=n):n=i+1}else n+=1;(!s||n>=e.length)&&a.push(e.substring(t,e.length))}return a}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[4447,580,9398,4999],()=>r(56230));module.exports=i})();