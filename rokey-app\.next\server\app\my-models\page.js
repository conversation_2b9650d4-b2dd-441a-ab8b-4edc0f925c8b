(()=>{var e={};e.id=5690,e.ids=[5690],e.modules={192:(e,t,s)=>{Promise.resolve().then(s.bind(s,39482))},2969:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(43210);let r=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"}))})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5097:(e,t,s)=>{"use strict";s.d(t,{_:()=>l});var a=s(43210);let r={};function l(){let[e,t]=(0,a.useState)({}),s=(0,a.useRef)({}),l=(0,a.useCallback)(e=>{let t=r[e];return!!t&&!(Date.now()-t.timestamp>3e5)&&!t.isLoading},[]),n=(0,a.useCallback)(e=>{let t=r[e];return!t||t.isLoading?null:Date.now()-t.timestamp>3e5?(delete r[e],null):t.data},[]),i=(0,a.useCallback)(async(e,a="medium")=>{if(l(e))return n(e);if(r[e]?.isLoading)return null;s.current[e]&&s.current[e].abort();let i=new AbortController;s.current[e]=i,r[e]={data:{},timestamp:Date.now(),isLoading:!0},t(t=>({...t,[e]:"loading"}));try{"low"===a?await new Promise(e=>setTimeout(e,200)):"medium"===a&&await new Promise(e=>setTimeout(e,50));let[s,l,n,o,d]=await Promise.allSettled([fetch("/api/custom-configs",{signal:i.signal}),fetch(`/api/keys?custom_config_id=${e}`,{signal:i.signal}),fetch("/api/user/custom-roles",{signal:i.signal}),fetch("/api/providers/list-models",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({}),signal:i.signal}),fetch(`/api/custom-configs/${e}/default-chat-key`,{signal:i.signal})]),c=null,m=[],u=[],x=[],p=null;if("fulfilled"===s.status&&s.value.ok&&(c=(await s.value.json()).find(t=>t.id===e)),"fulfilled"===l.status&&l.value.ok&&(m=await l.value.json()),"fulfilled"===n.status&&n.value.ok&&(u=await n.value.json()),"fulfilled"===o.status&&o.value.ok&&(x=(await o.value.json()).models||[]),"fulfilled"===d.status&&d.value.ok){let e=await d.value.json();p=e?.id||null}let g={configDetails:c,apiKeys:m,userCustomRoles:u,models:x,defaultChatKeyId:p};return r[e]={data:g,timestamp:Date.now(),isLoading:!1},t(t=>({...t,[e]:"success"})),g}catch(s){if("AbortError"===s.name)return null;return delete r[e],t(t=>({...t,[e]:"error"})),null}finally{delete s.current[e]}},[l,n]),o=(0,a.useCallback)(e=>({onMouseEnter:()=>{l(e)||i(e,"high")}}),[i,l]),d=(0,a.useCallback)(e=>{delete r[e],t(t=>{let s={...t};return delete s[e],s})},[]),c=(0,a.useCallback)(()=>{Object.keys(r).forEach(e=>{delete r[e]}),t({})},[]);return{prefetchManageKeysData:i,getCachedData:n,isCached:l,createHoverPrefetch:o,clearCache:d,clearAllCache:c,getStatus:(0,a.useCallback)(t=>e[t]||"idle",[e]),getCacheInfo:(0,a.useCallback)(()=>({cachedConfigs:Object.keys(r),cacheSize:Object.keys(r).length,totalCacheAge:Object.values(r).reduce((e,t)=>e+(Date.now()-t.timestamp),0)/Object.keys(r).length}),[]),prefetchStatus:e}}},8345:(e,t,s)=>{Promise.resolve().then(s.bind(s,47417))},9776:(e,t,s)=>{"use strict";s.d(t,{B0:()=>r,F6:()=>l});var a=s(60687);function r({className:e=""}){return(0,a.jsx)("div",{className:`glass rounded-2xl p-6 animate-pulse ${e}`,children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gray-700 rounded-xl"}),(0,a.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-700 rounded w-3/4"}),(0,a.jsx)("div",{className:"h-3 bg-gray-700 rounded w-1/2"})]})]})})}function l({rows:e=5,columns:t=4}){return(0,a.jsx)("div",{className:"glass rounded-2xl overflow-hidden",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"bg-gray-800 p-4 border-b border-gray-700",children:(0,a.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:`repeat(${t}, 1fr)`},children:Array.from({length:t}).map((e,t)=>(0,a.jsx)("div",{className:"h-4 bg-gray-700 rounded"},t))})}),Array.from({length:e}).map((e,s)=>(0,a.jsx)("div",{className:"p-4 border-b border-gray-700 last:border-b-0",children:(0,a.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:`repeat(${t}, 1fr)`},children:Array.from({length:t}).map((e,t)=>(0,a.jsx)("div",{className:"h-4 bg-gray-700 rounded"},t))})},s))]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13344:(e,t,s)=>{Promise.resolve().then(s.bind(s,69188))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20404:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});var a=s(43210);function r(){let[e,t]=(0,a.useState)({isOpen:!1,isLoading:!1,title:"",message:"",confirmText:"Confirm",cancelText:"Cancel",type:"danger",onConfirm:()=>{}}),s=(0,a.useCallback)((e,s)=>{t({isOpen:!0,isLoading:!1,title:e.title,message:e.message,confirmText:e.confirmText||"Confirm",cancelText:e.cancelText||"Cancel",type:e.type||"danger",onConfirm:async()=>{t(e=>({...e,isLoading:!0}));try{await s(),t(e=>({...e,isOpen:!1,isLoading:!1}))}catch(e){throw t(e=>({...e,isLoading:!1})),e}}})},[]),r=(0,a.useCallback)(()=>{t(e=>({...e,isOpen:!1,isLoading:!1}))},[]);return{...e,showConfirmation:s,hideConfirmation:r}}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},39482:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h});var a=s(60687),r=s(43210),l=s(85814),n=s.n(l),i=s(71031),o=s(97450),d=s(2969),c=s(71178),m=s(26403),u=s(50181),x=s(20404),p=s(9776),g=s(5097);function h(){let[e,t]=(0,r.useState)([]),[s,l]=(0,r.useState)(!0),[h,f]=(0,r.useState)(null),[y,b]=(0,r.useState)(""),[v,j]=(0,r.useState)(!1),w=(0,x.Z)(),[N,C]=(0,r.useState)(!1),{createHoverPrefetch:k,prefetchManageKeysData:A}=(0,g._)(),P=async()=>{l(!0),f(null);try{let e=await fetch("/api/custom-configs");if(!e.ok){let t=await e.json();throw Error(t.error||"Failed to fetch configurations")}let s=await e.json();t(s)}catch(e){f(e.message)}finally{l(!1)}},T=async e=>{if(e.preventDefault(),!y.trim())return void f("Configuration name cannot be empty.");j(!0),f(null);try{let e=await fetch("/api/custom-configs",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:y})}),t=await e.json();if(!e.ok)throw Error(t.details||t.error||"Failed to create configuration");b(""),C(!1),await P()}catch(e){f(e.message)}finally{j(!1)}},M=(e,t)=>{w.showConfirmation({title:"Delete Configuration",message:`Are you sure you want to delete "${t}"? This will permanently remove the configuration and all associated API keys. This action cannot be undone.`,confirmText:"Delete Configuration",cancelText:"Cancel",type:"danger"},async()=>{f(null);try{let t=await fetch(`/api/custom-configs/${e}`,{method:"DELETE"}),s=await t.json();if(!t.ok)throw Error(s.details||s.error||"Failed to delete configuration");await P()}catch(e){throw f(`Failed to delete: ${e.message}`),e}})};return(0,a.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-4xl font-bold text-gray-900",children:"My API Models"}),(0,a.jsx)("p",{className:"text-gray-600 mt-2",children:"Manage your custom API configurations and keys"})]}),(0,a.jsxs)("button",{onClick:()=>C(!N),className:N?"btn-secondary":"btn-primary",children:[(0,a.jsx)(i.A,{className:"h-4 w-4 mr-2"}),N?"Cancel":"Create New Model"]})]}),h&&(0,a.jsx)("div",{className:"card border-red-200 bg-red-50 p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),(0,a.jsx)("p",{className:"text-red-800",children:h})]})}),N&&(0,a.jsxs)("div",{className:"card max-w-md animate-scale-in p-6",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Create New Model"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Set up a new API configuration"})]}),(0,a.jsxs)("form",{onSubmit:T,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"configName",className:"block text-sm font-medium text-gray-700 mb-2",children:"Model Name"}),(0,a.jsx)("input",{type:"text",id:"configName",value:y,onChange:e=>b(e.target.value),required:!0,className:"form-input",placeholder:"e.g., My Main Chat Assistant"})]}),(0,a.jsx)("button",{type:"submit",disabled:v,className:"btn-primary w-full",children:v?"Creating...":"Create Model"})]})]}),s&&(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Array.from({length:6}).map((e,t)=>(0,a.jsx)(p.B0,{},t))}),!s&&!e.length&&!h&&!N&&(0,a.jsx)("div",{className:"card text-center py-12",children:(0,a.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-orange-50 rounded-2xl flex items-center justify-center mx-auto mb-6 border border-orange-100",children:(0,a.jsx)(o.A,{className:"h-8 w-8 text-orange-600"})}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"No API Models Yet"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Create your first API model configuration to get started with RoKey."}),(0,a.jsxs)("button",{onClick:()=>C(!0),className:"btn-primary",children:[(0,a.jsx)(i.A,{className:"h-4 w-4 mr-2"}),"Create Your First Model"]})]})}),!s&&e.length>0&&(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map((e,t)=>(0,a.jsxs)("div",{className:"card p-6 hover:shadow-lg transition-all duration-200 animate-slide-in",style:{animationDelay:`${100*t}ms`},children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2 truncate",children:e.name}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center text-xs text-gray-600",children:[(0,a.jsx)(o.A,{className:"h-3 w-3 mr-1"}),"ID: ",e.id.slice(0,8),"..."]}),(0,a.jsxs)("div",{className:"flex items-center text-xs text-gray-600",children:[(0,a.jsx)(d.A,{className:"h-3 w-3 mr-1"}),"Created: ",new Date(e.created_at).toLocaleDateString()]})]})]}),(0,a.jsx)("div",{className:"w-12 h-12 bg-orange-50 rounded-xl flex items-center justify-center shrink-0 border border-orange-100",children:(0,a.jsx)(o.A,{className:"h-6 w-6 text-orange-600"})})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 mt-6",children:[(0,a.jsx)(n(),{href:`/my-models/${e.id}`,className:"flex-1",...k(e.id),children:(0,a.jsxs)("button",{className:"btn-primary w-full",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Manage Keys"]})}),(0,a.jsxs)("button",{onClick:()=>M(e.id,e.name),className:"btn-secondary text-red-600 hover:text-red-700 hover:bg-red-50",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]},e.id))}),(0,a.jsx)(u.A,{isOpen:w.isOpen,onClose:w.hideConfirmation,onConfirm:w.onConfirm,title:w.title,message:w.message,confirmText:w.confirmText,cancelText:w.cancelText,type:w.type,isLoading:w.isLoading})]})}},44793:(e,t,s)=>{Promise.resolve().then(s.bind(s,35291))},50181:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(60687);s(43210);var r=s(26403),l=s(59168),n=s(81836);function i({isOpen:e,onClose:t,onConfirm:s,title:i,message:o,confirmText:d="Delete",cancelText:c="Cancel",type:m="danger",isLoading:u=!1}){let x=(()=>{switch(m){case"danger":return{iconBg:"bg-red-100",iconColor:"text-red-600",confirmButton:"bg-red-600 hover:bg-red-700 focus:ring-red-500",icon:r.A};case"warning":return{iconBg:"bg-yellow-100",iconColor:"text-yellow-600",confirmButton:"bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500",icon:l.A};default:return{iconBg:"bg-blue-100",iconColor:"text-blue-600",confirmButton:"bg-blue-600 hover:bg-blue-700 focus:ring-blue-500",icon:l.A}}})(),p=x.icon;return e?(0,a.jsxs)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300",onClick:u?void 0:t}),(0,a.jsx)("div",{className:"flex min-h-full items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"relative w-full max-w-md transform overflow-hidden rounded-2xl bg-white shadow-2xl transition-all duration-300 scale-100 opacity-100",children:[(0,a.jsx)("div",{className:"relative px-6 pt-6",children:(0,a.jsx)("button",{onClick:t,disabled:u,className:"absolute top-4 right-4 p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:(0,a.jsx)(n.A,{className:"h-5 w-5"})})}),(0,a.jsxs)("div",{className:"px-6 pb-6",children:[(0,a.jsx)("div",{className:"mx-auto flex h-16 w-16 items-center justify-center rounded-full mb-6",children:(0,a.jsx)("div",{className:`${x.iconBg} rounded-full p-3`,children:(0,a.jsx)(p,{className:`h-8 w-8 ${x.iconColor}`})})}),(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3 text-center",children:i}),(0,a.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed mb-8 text-center",children:o}),(0,a.jsxs)("div",{className:"flex flex-col-reverse sm:flex-row sm:justify-center gap-3",children:[(0,a.jsx)("button",{type:"button",onClick:t,disabled:u,className:"w-full sm:w-auto px-6 py-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-xl hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200",children:c}),(0,a.jsx)("button",{type:"button",onClick:s,disabled:u,className:`w-full sm:w-auto px-6 py-3 text-sm font-medium text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 ${x.confirmButton}`,children:u?(0,a.jsxs)("div",{className:"flex items-center justify-center",children:[(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Processing..."]}):d})]})]})]})})]}):null}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69188:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\my-models\\page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},89507:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var a=s(65239),r=s(48088),l=s(88170),n=s.n(l),i=s(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let d={children:["",{children:["my-models",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,69188)),"C:\\RoKey App\\rokey-app\\src\\app\\my-models\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(s.bind(s,92529)),"C:\\RoKey App\\rokey-app\\src\\app\\my-models\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\RoKey App\\rokey-app\\src\\app\\my-models\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/my-models/page",pathname:"/my-models",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},92529:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var a=s(37413),r=s(47417);function l(){return(0,a.jsx)(r.MyModelsSkeleton,{})}}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4447,9631,1658,8481,6271,2646],()=>s(89507));module.exports=a})();